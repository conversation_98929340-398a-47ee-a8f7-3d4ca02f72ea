#!/usr/bin/env python3
"""
KianaDispatch Crypto Tool
=========================

A tool for encrypting and decrypting KianaDispatch responses used by miHoYo games.
Based on the implementation from Hi3Helper.EncTool.Parser.KianaDispatch.

Author: Collapse Launcher Team
License: MIT
"""

import base64
import hashlib
import json
import sys
import argparse
from typing import Optional, Union
from Crypto.Cipher import AES
from Crypto.Util.Padding import pad, unpad


class KianaDispatchCrypto:
    """
    KianaDispatch encryption/decryption utility class.
    
    This class implements the same encryption/decryption logic used by
    Collapse Launcher's KianaDispatch parser.
    """
    
    def __init__(self, dispatcher_key: str, version: str):
        """
        Initialize the crypto utility.
        
        Args:
            dispatcher_key: The dispatcher key from game configuration (e.g., "bgyzmmhy")
            version: Game version in format "major.minor" (e.g., "8.3")
        """
        self.dispatcher_key = dispatcher_key
        self.version = version
        self._aes_key = self._generate_aes_key()
    
    def _generate_aes_key(self) -> bytes:
        """
        Generate AES key following the same process as KianaDispatch.cs
        
        Process:
        1. Combine version + dispatcher_key (e.g., "8.3bgyzmmhy")
        2. Calculate MD5 hash
        3. Convert to lowercase hex string
        4. Take first 32 bytes as AES-256 key
        
        Returns:
            32-byte AES key
        """
        # Phase 1: Combine version and dispatcher key
        phase1_key = f"{self.version}{self.dispatcher_key}"
        
        # Phase 2: Calculate MD5 hash
        phase1_bytes = phase1_key.encode('utf-8')
        md5_hash = hashlib.md5(phase1_bytes).digest()
        
        # Phase 3: Convert to lowercase hex string
        phase2_key = md5_hash.hex().lower()
        
        # Phase 4: Take first 32 bytes as AES key
        aes_key = phase2_key.encode('utf-8')[:32]
        
        return aes_key
    
    def _is_encrypted(self, data: bytes) -> bool:
        """
        Check if the data is encrypted by looking at the first byte.
        
        Args:
            data: Raw response data
            
        Returns:
            True if encrypted (first byte != 0x7b '{'), False otherwise
        """
        if not data:
            return False
        return data[0] != 0x7b  # 0x7b is '{'
    
    def decrypt(self, encrypted_data: str) -> dict:
        """
        Decrypt KianaDispatch response.
        
        Args:
            encrypted_data: Base64 encoded encrypted response
            
        Returns:
            Decrypted JSON data as dictionary
            
        Raises:
            ValueError: If decryption fails
        """
        try:
            # Step 1: Base64 decode
            encrypted_bytes = base64.b64decode(encrypted_data)
            
            # Step 2: AES-256-ECB decrypt
            cipher = AES.new(self._aes_key, AES.MODE_ECB)
            decrypted_bytes = cipher.decrypt(encrypted_bytes)
            
            # Step 3: Remove PKCS7 padding and decode UTF-8
            try:
                # Try to unpad (in case padding was used)
                unpadded_bytes = unpad(decrypted_bytes, AES.block_size)
            except ValueError:
                # If unpadding fails, use original bytes
                unpadded_bytes = decrypted_bytes
            
            # Find the end of JSON (first null byte or end of data)
            json_end = unpadded_bytes.find(b'\x00')
            if json_end != -1:
                json_bytes = unpadded_bytes[:json_end]
            else:
                json_bytes = unpadded_bytes.rstrip(b'\x00')
            
            json_str = json_bytes.decode('utf-8')
            
            # Step 4: Parse JSON
            return json.loads(json_str)
            
        except Exception as e:
            raise ValueError(f"Decryption failed: {e}")
    
    def encrypt(self, json_data: Union[dict, str]) -> str:
        """
        Encrypt JSON data to KianaDispatch format.
        
        Args:
            json_data: Dictionary or JSON string to encrypt
            
        Returns:
            Base64 encoded encrypted data
            
        Raises:
            ValueError: If encryption fails
        """
        try:
            # Step 1: Convert to JSON string if needed
            if isinstance(json_data, dict):
                json_str = json.dumps(json_data, separators=(',', ':'), ensure_ascii=False)
            else:
                json_str = json_data
            
            # Step 2: Encode to UTF-8
            json_bytes = json_str.encode('utf-8')
            
            # Step 3: Pad to AES block size
            padded_bytes = pad(json_bytes, AES.block_size)
            
            # Step 4: AES-256-ECB encrypt
            cipher = AES.new(self._aes_key, AES.MODE_ECB)
            encrypted_bytes = cipher.encrypt(padded_bytes)
            
            # Step 5: Base64 encode
            return base64.b64encode(encrypted_bytes).decode('ascii')
            
        except Exception as e:
            raise ValueError(f"Encryption failed: {e}")
    
    def process_response(self, data: str) -> dict:
        """
        Process response data (auto-detect if encrypted or plain JSON).
        
        Args:
            data: Response data (encrypted or plain JSON)
            
        Returns:
            Parsed JSON data as dictionary
        """
        # Try to parse as JSON first
        try:
            return json.loads(data)
        except json.JSONDecodeError:
            pass
        
        # If JSON parsing fails, try to decrypt
        try:
            return self.decrypt(data)
        except ValueError:
            raise ValueError("Data is neither valid JSON nor valid encrypted data")


def main():
    """Main CLI interface."""
    parser = argparse.ArgumentParser(
        description="KianaDispatch Crypto Tool - Encrypt/Decrypt miHoYo game dispatch responses",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Decrypt a response
  python kiana_dispatch_crypto.py decrypt -k bgyzmmhy -v 8.3 -d "oG7PvQY0q1ZqrCiH..."
  
  # Encrypt JSON data
  python kiana_dispatch_crypto.py encrypt -k bgyzmmhy -v 8.3 -j '{"retcode":0,"message":"OK"}'
  
  # Process from file
  python kiana_dispatch_crypto.py decrypt -k bgyzmmhy -v 8.3 -f encrypted_response.txt
  
  # Auto-detect and process
  python kiana_dispatch_crypto.py auto -k bgyzmmhy -v 8.3 -d "some_data"
        """
    )
    
    subparsers = parser.add_subparsers(dest='command', help='Available commands')
    
    # Common arguments
    def add_common_args(subparser):
        subparser.add_argument('-k', '--key', required=True, 
                             help='Dispatcher key (e.g., bgyzmmhy)')
        subparser.add_argument('-v', '--version', required=True,
                             help='Game version (e.g., 8.3)')
        subparser.add_argument('-f', '--file', 
                             help='Input file path')
        subparser.add_argument('-o', '--output',
                             help='Output file path')
    
    # Decrypt command
    decrypt_parser = subparsers.add_parser('decrypt', help='Decrypt encrypted response')
    add_common_args(decrypt_parser)
    decrypt_parser.add_argument('-d', '--data', 
                               help='Encrypted data string')
    
    # Encrypt command
    encrypt_parser = subparsers.add_parser('encrypt', help='Encrypt JSON data')
    add_common_args(encrypt_parser)
    encrypt_parser.add_argument('-j', '--json',
                               help='JSON data to encrypt')
    
    # Auto-detect command
    auto_parser = subparsers.add_parser('auto', help='Auto-detect and process data')
    add_common_args(auto_parser)
    auto_parser.add_argument('-d', '--data',
                            help='Data to process')
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return 1
    
    try:
        crypto = KianaDispatchCrypto(args.key, args.version)
        
        # Get input data
        if args.file:
            with open(args.file, 'r', encoding='utf-8') as f:
                input_data = f.read().strip()
        elif hasattr(args, 'data') and args.data:
            input_data = args.data
        elif hasattr(args, 'json') and args.json:
            input_data = args.json
        else:
            print("Error: No input data provided", file=sys.stderr)
            return 1
        
        # Process based on command
        if args.command == 'decrypt':
            result = crypto.decrypt(input_data)
            output = json.dumps(result, indent=2, ensure_ascii=False)
        elif args.command == 'encrypt':
            result = crypto.encrypt(input_data)
            output = result
        elif args.command == 'auto':
            result = crypto.process_response(input_data)
            output = json.dumps(result, indent=2, ensure_ascii=False)
        
        # Output result
        if args.output:
            with open(args.output, 'w', encoding='utf-8') as f:
                f.write(output)
            print(f"Result saved to {args.output}")
        else:
            print(output)
        
        return 0
        
    except Exception as e:
        print(f"Error: {e}", file=sys.stderr)
        return 1


if __name__ == '__main__':
    sys.exit(main())
