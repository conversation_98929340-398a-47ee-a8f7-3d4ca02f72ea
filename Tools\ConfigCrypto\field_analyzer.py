#!/usr/bin/env python3
"""
Configuration Field Analyzer

This tool analyzes CollapseLauncher configuration files to identify
encrypted fields and provide insights about the configuration structure.
"""

import argparse
import base64
import json
import re
from pathlib import Path
from typing import Dict, Any, List, Tuple, Optional


class ConfigFieldAnalyzer:
    """Analyzer for CollapseLauncher configuration fields."""
    
    def __init__(self):
        """Initialize the analyzer."""
        self.known_encrypted_fields = {
            'GameDispatchURL',
            'GameDispatchURLTemplate',
            'GameGatewayURLTemplate',
            'GameGatewayDefault',
            'LauncherSpriteURL',
            'LauncherResourceURL',
            'LauncherPluginURL',
            'LauncherNewsURL',
            'LauncherCPSType',
            'LauncherId',
            'LauncherGameId',
            'LauncherBizName',
            'DispatcherKey',
            'GameDispatchChannelName',
            'GameDispatchArrayURL',
            'ProtoDispatchKey',
            'LauncherResourceChunksURL',
            # Nested fields in LauncherResourceChunksURL
            'BranchUrl',
            'MainUrl',
            'PreloadUrl',
            'PatchUrl',
            'LauncherGameInfoDisplayURL',
            'LauncherGameChannelSDKURL'
        }
        
        self.field_descriptions = {
            'GameDispatchURL': 'Game dispatch server URL',
            'GameDispatchURLTemplate': 'Template for game dispatch URLs',
            'GameGatewayURLTemplate': 'Template for game gateway URLs', 
            'GameGatewayDefault': 'Default game gateway',
            'LauncherSpriteURL': 'Launcher sprite/image URL',
            'LauncherResourceURL': 'Launcher resource download URL',
            'LauncherPluginURL': 'Launcher plugin download URL',
            'LauncherNewsURL': 'Launcher news feed URL',
            'LauncherCPSType': 'Launcher CPS (Content Protection System) type',
            'LauncherId': 'Unique launcher identifier',
            'LauncherGameId': 'Game identifier for launcher',
            'LauncherBizName': 'Business name for launcher',
            'DispatcherKey': 'Dispatcher encryption key',
            'GameDispatchChannelName': 'Game dispatch channel name',
            'GameDispatchArrayURL': 'Array of game dispatch URLs',
            'ProtoDispatchKey': 'Protocol dispatcher key',
            'LauncherResourceChunksURL': 'Launcher resource chunks URL configuration',
            'BranchUrl': 'Branch-specific resource URL',
            'MainUrl': 'Main resource download URL',
            'PreloadUrl': 'Preload resource URL',
            'PatchUrl': 'Patch resource URL',
            'LauncherGameInfoDisplayURL': 'LauncherGameInfoDisplay resource URL',
            'LauncherGameChannelSDKURL': 'LauncherGameChannelSDK resource URL'
        }
    
    def is_base64_encoded(self, data: str) -> bool:
        """
        Check if a string is valid base64 encoded data.
        
        Args:
            data: String to check
            
        Returns:
            True if valid base64, False otherwise
        """
        try:
            if len(data) % 4 != 0:
                return False
            
            decoded = base64.b64decode(data, validate=True)
            return len(decoded) > 0
        except Exception:
            return False
    
    def is_collapse_encrypted(self, data: str) -> bool:
        """
        Check if a string is Collapse-encrypted data (ServeV3 format).

        Args:
            data: String to check

        Returns:
            True if Collapse-encrypted, False otherwise
        """
        try:
            if not self.is_base64_encoded(data):
                return False

            decoded = base64.b64decode(data)

            # Check for ServeV3 format (CollapseSignature = 7310310183885631299)
            if len(decoded) >= 8:
                signature = int.from_bytes(decoded[:8], byteorder='little')
                if signature == 7310310183885631299:
                    return True

            # Fallback: check for "Collapse" prefix (legacy format)
            return decoded.startswith(b"Collapse")
        except Exception:
            return False
    
    def analyze_field_value(self, field_name: str, value: Any) -> Dict[str, Any]:
        """
        Analyze a single field value.
        
        Args:
            field_name: Name of the field
            value: Value to analyze
            
        Returns:
            Analysis results dictionary
        """
        analysis = {
            'field_name': field_name,
            'type': type(value).__name__,
            'is_known_encrypted': field_name in self.known_encrypted_fields,
            'description': self.field_descriptions.get(field_name, 'Unknown field'),
            'encryption_status': 'unknown'
        }
        
        if isinstance(value, str):
            analysis['length'] = len(value)
            analysis['is_base64'] = self.is_base64_encoded(value)
            analysis['is_collapse_encrypted'] = self.is_collapse_encrypted(value)
            
            if analysis['is_collapse_encrypted']:
                analysis['encryption_status'] = 'encrypted'
                # Try to get some info about the encrypted data
                try:
                    decoded = base64.b64decode(value)
                    analysis['encrypted_size'] = len(decoded)
                    analysis['has_collapse_prefix'] = decoded.startswith(b"Collapse")
                except:
                    pass
            elif analysis['is_base64']:
                analysis['encryption_status'] = 'possibly_encoded'
            else:
                analysis['encryption_status'] = 'plaintext'
                
        elif isinstance(value, list):
            analysis['length'] = len(value)
            analysis['item_types'] = list(set(type(item).__name__ for item in value))
            
            # Check if array contains encrypted strings
            encrypted_items = 0
            for item in value:
                if isinstance(item, str) and self.is_collapse_encrypted(item):
                    encrypted_items += 1
            
            analysis['encrypted_items'] = encrypted_items
            if encrypted_items > 0:
                analysis['encryption_status'] = 'partially_encrypted'
            else:
                analysis['encryption_status'] = 'plaintext'
                
        elif isinstance(value, dict):
            analysis['keys'] = list(value.keys())
            analysis['key_count'] = len(value)
            
            # Check nested values for encryption
            encrypted_values = 0
            for k, v in value.items():
                if isinstance(v, str) and self.is_collapse_encrypted(v):
                    encrypted_values += 1
            
            analysis['encrypted_values'] = encrypted_values
            if encrypted_values > 0:
                analysis['encryption_status'] = 'partially_encrypted'
            else:
                analysis['encryption_status'] = 'plaintext'
        
        return analysis
    
    def analyze_config_file(self, config_path: str) -> Dict[str, Any]:
        """
        Analyze a complete configuration file.
        
        Args:
            config_path: Path to the configuration file
            
        Returns:
            Complete analysis results
        """
        with open(config_path, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        analysis = {
            'file_path': config_path,
            'total_fields': len(config),
            'fields': {},
            'summary': {
                'encrypted_fields': 0,
                'plaintext_fields': 0,
                'unknown_fields': 0,
                'total_encrypted_strings': 0
            }
        }
        
        # Analyze each field
        for field_name, value in config.items():
            field_analysis = self.analyze_field_value(field_name, value)
            analysis['fields'][field_name] = field_analysis
            
            # Update summary
            if field_analysis['encryption_status'] == 'encrypted':
                analysis['summary']['encrypted_fields'] += 1
                analysis['summary']['total_encrypted_strings'] += 1
            elif field_analysis['encryption_status'] == 'partially_encrypted':
                analysis['summary']['encrypted_fields'] += 1
                if 'encrypted_items' in field_analysis:
                    analysis['summary']['total_encrypted_strings'] += field_analysis['encrypted_items']
                elif 'encrypted_values' in field_analysis:
                    analysis['summary']['total_encrypted_strings'] += field_analysis['encrypted_values']
            elif field_analysis['encryption_status'] == 'plaintext':
                analysis['summary']['plaintext_fields'] += 1
            else:
                analysis['summary']['unknown_fields'] += 1
        
        return analysis
    
    def print_analysis_report(self, analysis: Dict[str, Any], show_details: bool = True) -> None:
        """
        Print a formatted analysis report.
        
        Args:
            analysis: Analysis results from analyze_config_file
            show_details: Whether to show detailed field information
        """
        print("=" * 70)
        print(f"Configuration File Analysis: {analysis['file_path']}")
        print("=" * 70)
        
        # Summary
        summary = analysis['summary']
        print(f"📊 Summary:")
        print(f"   Total Fields: {analysis['total_fields']}")
        print(f"   Encrypted Fields: {summary['encrypted_fields']}")
        print(f"   Plaintext Fields: {summary['plaintext_fields']}")
        print(f"   Unknown Fields: {summary['unknown_fields']}")
        print(f"   Total Encrypted Strings: {summary['total_encrypted_strings']}")
        print()
        
        if show_details:
            # Encrypted fields
            print("🔒 Encrypted Fields:")
            encrypted_fields = [
                (name, info) for name, info in analysis['fields'].items()
                if info['encryption_status'] in ['encrypted', 'partially_encrypted']
            ]
            
            if encrypted_fields:
                for field_name, field_info in encrypted_fields:
                    status_icon = "🔐" if field_info['encryption_status'] == 'encrypted' else "🔓"
                    print(f"   {status_icon} {field_name}")
                    print(f"      Type: {field_info['type']}")
                    print(f"      Description: {field_info['description']}")
                    
                    if field_info['type'] == 'str':
                        print(f"      Length: {field_info['length']} chars")
                        if 'encrypted_size' in field_info:
                            print(f"      Encrypted Size: {field_info['encrypted_size']} bytes")
                    elif field_info['type'] == 'list':
                        print(f"      Items: {field_info['length']}")
                        if 'encrypted_items' in field_info:
                            print(f"      Encrypted Items: {field_info['encrypted_items']}")
                    elif field_info['type'] == 'dict':
                        print(f"      Keys: {field_info['key_count']}")
                        if 'encrypted_values' in field_info:
                            print(f"      Encrypted Values: {field_info['encrypted_values']}")
                    print()
            else:
                print("   No encrypted fields found.")
                print()
            
            # Plaintext fields
            print("📝 Plaintext Fields:")
            plaintext_fields = [
                (name, info) for name, info in analysis['fields'].items()
                if info['encryption_status'] == 'plaintext'
            ]
            
            for field_name, field_info in plaintext_fields[:10]:  # Show first 10
                print(f"   📄 {field_name} ({field_info['type']})")
            
            if len(plaintext_fields) > 10:
                print(f"   ... and {len(plaintext_fields) - 10} more")
            print()
    
    def generate_decryption_script(self, analysis: Dict[str, Any], output_path: str) -> None:
        """
        Generate a decryption script based on analysis results.
        
        Args:
            analysis: Analysis results
            output_path: Path to save the script
        """
        encrypted_fields = [
            name for name, info in analysis['fields'].items()
            if info['encryption_status'] in ['encrypted', 'partially_encrypted']
        ]
        
        script_content = f'''#!/usr/bin/env python3
"""
Auto-generated decryption script for {Path(analysis['file_path']).name}
Generated by ConfigFieldAnalyzer
"""

import sys
import os
sys.path.append(os.path.dirname(__file__))

from crypto_tool import CollapseDecryptor

def main():
    # Initialize decryptor with master key
    master_key_path = input("Enter master key file path: ")
    decryptor = CollapseDecryptor(master_key_path)
    
    # Input and output files
    input_file = "{analysis['file_path']}"
    output_file = input("Enter output file path: ")
    
    # Fields to decrypt (identified by analyzer)
    fields_to_decrypt = {encrypted_fields}
    
    # Perform decryption
    decryptor.decrypt_config_file(input_file, output_file, fields_to_decrypt)
    print(f"Decryption complete! Output saved to: {{output_file}}")

if __name__ == "__main__":
    main()
'''
        
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(script_content)
        
        print(f"✓ Decryption script generated: {output_path}")


def main():
    """Main function to handle command line arguments."""
    parser = argparse.ArgumentParser(
        description="Analyze CollapseLauncher configuration files"
    )
    
    parser.add_argument(
        'config_file',
        help='Path to configuration file to analyze'
    )
    
    parser.add_argument(
        '--output-report',
        help='Save analysis report to file'
    )
    
    parser.add_argument(
        '--generate-script',
        help='Generate decryption script based on analysis'
    )
    
    parser.add_argument(
        '--brief',
        action='store_true',
        help='Show brief summary only'
    )
    
    args = parser.parse_args()
    
    try:
        analyzer = ConfigFieldAnalyzer()
        analysis = analyzer.analyze_config_file(args.config_file)
        
        # Print report
        analyzer.print_analysis_report(analysis, show_details=not args.brief)
        
        # Save report if requested
        if args.output_report:
            with open(args.output_report, 'w', encoding='utf-8') as f:
                import json
                json.dump(analysis, f, indent=2, ensure_ascii=False)
            print(f"✓ Analysis report saved to: {args.output_report}")
        
        # Generate script if requested
        if args.generate_script:
            analyzer.generate_decryption_script(analysis, args.generate_script)
        
        return 0
        
    except Exception as e:
        print(f"Error: {e}")
        return 1


if __name__ == "__main__":
    exit(main())
