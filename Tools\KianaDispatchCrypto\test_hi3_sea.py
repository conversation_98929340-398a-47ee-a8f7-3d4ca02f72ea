#!/usr/bin/env python3
"""
崩坏3东南亚服测试脚本
===================

测试崩坏3东南亚服的 dispatch 系统和资源获取。
"""

import asyncio
import json
import time
from game_resource_fetcher import GameResourceFetcher
from kiana_dispatch_crypto import KianaDispatchCrypto


async def test_hi3_sea_dispatch():
    """测试崩坏3东南亚服的 dispatch 系统"""
    
    print("🌏 崩坏3东南亚服 Dispatch 测试")
    print("=" * 50)
    
    # 东南亚服配置
    config = {
        "dispatcher_key": "bgyzmmhy",
        "version": "8.3",
        "dispatch_urls": [
            "https://osglobal1.honkaiimpact3.com/query_dispatch",
            "https://osglobal2.honkaiimpact3.com/query_dispatch"
        ],
        "channel_name": "os_pc",
        "gateway_default": "overseas01",
        "token": "82cb3ccb0be3ee59d8b51e2139c18914"
    }
    
    print("📋 配置信息:")
    print(f"   调度密钥: {config['dispatcher_key']}")
    print(f"   游戏版本: {config['version']}")
    print(f"   渠道名称: {config['channel_name']}")
    print(f"   默认网关: {config['gateway_default']}")
    print(f"   认证令牌: {config['token'][:20]}...")
    print()
    
    # 测试每个分发服务器
    for i, dispatch_url in enumerate(config["dispatch_urls"], 1):
        print(f"🔄 测试分发服务器 {i}: {dispatch_url}")
        print("-" * 40)
        
        try:
            async with GameResourceFetcher(config["dispatcher_key"], config["version"]) as fetcher:
                # 构建完整的 dispatch URL
                timestamp = int(time.time())
                full_version = f"{config['version']}.0"
                query_params = f"?version={full_version}_{config['channel_name']}&t={timestamp}&lang=EN&token={config['token']}"
                full_url = dispatch_url + query_params
                
                print(f"📡 请求 URL: {full_url}")
                
                # 获取并解密响应
                dispatch_data = await fetcher.fetch_dispatch_response(
                    dispatch_url, 
                    config["channel_name"], 
                    config["token"]
                )
                
                print("✅ 分发服务器响应成功!")
                print(f"   返回码: {dispatch_data.get('retcode')}")
                
                # 显示可用服务器
                region_list = dispatch_data.get("region_list", [])
                print(f"   可用服务器数量: {len(region_list)}")
                
                if region_list:
                    print("\n   🌐 可用服务器:")
                    for region in region_list:
                        name = region.get("name", "unknown")
                        title = region.get("title", "无标题")
                        print(f"      • {name}: {title}")
                
                # 查找推荐的网关服务器
                target_server = None
                for region in region_list:
                    if region.get("name") == config["gateway_default"]:
                        target_server = region
                        break
                
                if target_server:
                    print(f"\n   🎯 找到目标服务器: {target_server['name']}")
                    
                    # 测试游戏服务器查询
                    print(f"\n🔄 测试游戏服务器查询...")
                    gameserver_data = await fetcher.fetch_gameserver_info(
                        dispatch_data, 
                        config["gateway_default"],
                        config["channel_name"],
                        config["token"]
                    )
                    
                    print("✅ 游戏服务器响应成功!")
                    print(f"   返回码: {gameserver_data.get('retcode')}")
                    print(f"   数据就绪: {gameserver_data.get('is_data_ready', 'unknown')}")
                    
                    # 提取资源 URL
                    asset_bundle_urls, external_resource_urls = fetcher.extract_resource_urls(gameserver_data)
                    
                    if asset_bundle_urls:
                        print(f"\n   📦 资源包 URL:")
                        for url in asset_bundle_urls:
                            print(f"      {url}")
                        
                        # 构建下载 URL
                        base_url = asset_bundle_urls[0]
                        pkg_version_url = fetcher.build_pkg_version_url(base_url)
                        
                        print(f"\n   📄 pkg_version URL:")
                        print(f"      {pkg_version_url}")
                        
                        # 尝试获取 pkg_version
                        print(f"\n🔄 尝试获取 pkg_version 文件...")
                        pkg_content = await fetcher.fetch_pkg_version(pkg_version_url)
                        
                        if pkg_content:
                            print("✅ pkg_version 文件获取成功!")
                            print(f"   文件大小: {len(pkg_content)} 字节")
                            
                            # 显示前几行内容
                            lines = pkg_content.strip().split('\n')
                            print(f"   总行数: {len(lines)}")
                            if lines:
                                print(f"\n   📝 内容预览:")
                                for i, line in enumerate(lines[:3]):
                                    print(f"      {i+1}: {line[:60]}{'...' if len(line) > 60 else ''}")
                        else:
                            print("⚠️  pkg_version 文件不可用")
                    
                    else:
                        print("⚠️  未找到资源包 URL")
                else:
                    print(f"⚠️  未找到目标服务器: {config['gateway_default']}")
                
                print(f"\n✅ 服务器 {i} 测试完成!")
                break  # 成功后退出循环
                
        except Exception as e:
            print(f"❌ 服务器 {i} 测试失败: {e}")
            continue
        
        print()
    
    print("🎉 东南亚服测试完成!")


def test_sea_encryption():
    """测试东南亚服的加密解密"""
    
    print("\n🔐 东南亚服加密解密测试")
    print("=" * 50)
    
    # 创建加密工具
    crypto = KianaDispatchCrypto("bgyzmmhy", "8.3")
    
    # 测试数据
    test_data = {
        "retcode": 0,
        "region_list": [
            {
                "name": "overseas01",
                "title": "Overseas Server 01",
                "dispatch_url": "https://example-sea.honkaiimpact3.com/query_gameserver"
            }
        ]
    }
    
    print("📝 测试数据:")
    print(json.dumps(test_data, indent=2))
    
    # 加密测试
    print("\n🔒 加密测试...")
    try:
        encrypted = crypto.encrypt(test_data)
        print("✅ 加密成功!")
        print(f"   加密结果长度: {len(encrypted)} 字符")
        print(f"   加密结果预览: {encrypted[:50]}...")
        
        # 解密测试
        print("\n🔓 解密测试...")
        decrypted = crypto.decrypt(encrypted)
        print("✅ 解密成功!")
        print("   解密结果:")
        print(json.dumps(decrypted, indent=2))
        
        # 验证一致性
        if decrypted == test_data:
            print("\n✅ 加密解密一致性验证通过!")
        else:
            print("\n❌ 加密解密一致性验证失败!")
            
    except Exception as e:
        print(f"❌ 加密解密测试失败: {e}")


def show_sea_vs_cn_comparison():
    """显示东南亚服与国服的对比"""
    
    print("\n📊 东南亚服 vs 国服对比")
    print("=" * 50)
    
    comparison = {
        "配置项": ["区域", "调度密钥", "分发服务器", "渠道名称", "默认网关", "认证令牌", "语言支持"],
        "国服 (Hi3CN)": [
            "Mainland China",
            "bgyzmmhy", 
            "global1.bh3.com",
            "gf_pc",
            "pc01",
            "a9366694c30364d479dd142f9cf0de54",
            "仅中文"
        ],
        "东南亚服 (Hi3SEA)": [
            "Southeast Asia",
            "bgyzmmhy",
            "osglobal1.honkaiimpact3.com", 
            "os_pc",
            "overseas01",
            "82cb3ccb0be3ee59d8b51e2139c18914",
            "多语言 (中英越泰印尼)"
        ]
    }
    
    for i, item in enumerate(comparison["配置项"]):
        print(f"{item}:")
        print(f"   国服:     {comparison['国服 (Hi3CN)'][i]}")
        print(f"   东南亚服: {comparison['东南亚服 (Hi3SEA)'][i]}")
        print()
    
    print("🔧 使用相同工具的原因:")
    print("• 相同的调度密钥 (bgyzmmhy)")
    print("• 相同的加密算法 (AES-256-ECB)")
    print("• 相同的协议结构")
    print("• 仅 URL 和参数不同")


async def main():
    """主函数"""
    
    print("🌏 崩坏3东南亚服完整测试套件")
    print("=" * 60)
    
    # 显示对比信息
    show_sea_vs_cn_comparison()
    
    # 加密解密测试
    test_sea_encryption()
    
    # 询问是否进行网络测试
    print("\n" + "=" * 60)
    print("⚠️  网络测试需要连接到东南亚服务器")
    choice = input("是否进行网络测试? (y/N): ").strip().lower()
    
    if choice in ['y', 'yes']:
        await test_hi3_sea_dispatch()
    else:
        print("跳过网络测试")
    
    print("\n🎉 所有测试完成!")
    print("\n💡 使用方法:")
    print("python game_resource_fetcher.py -k bgyzmmhy -v 8.3 \\")
    print("  -u 'https://osglobal1.honkaiimpact3.com/query_dispatch' \\")
    print("  -s overseas01")


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n\n用户取消操作")
    except Exception as e:
        print(f"\n错误: {e}")
