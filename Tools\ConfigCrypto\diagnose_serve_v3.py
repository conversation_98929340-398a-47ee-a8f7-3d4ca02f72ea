#!/usr/bin/env python3
"""
ServeV3 数据诊断工具
==================

用于诊断和分析 ServeV3 格式数据的问题。
"""

import base64
import json
import sys
import argparse
from typing import Optional


def analyze_serve_v3_data(data: bytes) -> dict:
    """分析 ServeV3 数据格式"""
    
    analysis = {
        "total_length": len(data),
        "is_valid_length": len(data) >= 32,
        "signature": None,
        "is_valid_signature": False,
        "attributes": None,
        "compression_type": None,
        "is_encrypted": None,
        "compressed_size": None,
        "decompressed_size": None,
        "header_hex": data[:32].hex() if len(data) >= 32 else data.hex(),
        "issues": []
    }
    
    if len(data) < 32:
        analysis["issues"].append(f"Data too short: {len(data)} bytes (minimum 32 required)")
        return analysis
    
    # 检查签名
    signature = int.from_bytes(data[:8], byteorder='little')
    expected_signature = 7310310183885631299
    analysis["signature"] = signature
    analysis["is_valid_signature"] = signature == expected_signature
    
    if not analysis["is_valid_signature"]:
        analysis["issues"].append(f"Invalid signature: {signature} (expected {expected_signature})")
    
    # 读取属性
    attrib_number = int.from_bytes(data[8:16], byteorder='little')
    analysis["attributes"] = attrib_number
    analysis["compression_type"] = attrib_number & 0xFF
    analysis["is_encrypted"] = (attrib_number >> 8) & 0xFF == 1
    
    # 读取大小
    compressed_size = int.from_bytes(data[16:24], byteorder='little')
    decompressed_size = int.from_bytes(data[24:32], byteorder='little')
    analysis["compressed_size"] = compressed_size
    analysis["decompressed_size"] = decompressed_size
    
    # 验证大小
    if compressed_size < 0:
        analysis["issues"].append(f"Invalid compressed size: {compressed_size}")
    if decompressed_size < 0:
        analysis["issues"].append(f"Invalid decompressed size: {decompressed_size}")
    if compressed_size > len(data) - 32:
        analysis["issues"].append(f"Compressed size ({compressed_size}) exceeds available data ({len(data) - 32})")
    
    # 检查压缩类型
    if analysis["compression_type"] not in [0, 1, 2]:
        analysis["issues"].append(f"Unknown compression type: {analysis['compression_type']}")
    
    return analysis


def hex_dump(data: bytes, max_bytes: int = 256) -> str:
    """生成十六进制转储"""
    lines = []
    for i in range(0, min(len(data), max_bytes), 16):
        chunk = data[i:i+16]
        hex_part = ' '.join(f'{b:02x}' for b in chunk)
        ascii_part = ''.join(chr(b) if 32 <= b <= 126 else '.' for b in chunk)
        lines.append(f"{i:08x}: {hex_part:<48} |{ascii_part}|")
    
    if len(data) > max_bytes:
        lines.append(f"... ({len(data) - max_bytes} more bytes)")
    
    return '\n'.join(lines)


def diagnose_base64_data(base64_data: str) -> dict:
    """诊断 base64 编码的数据"""
    
    diagnosis = {
        "base64_length": len(base64_data),
        "is_valid_base64": False,
        "decoded_data": None,
        "serve_v3_analysis": None,
        "issues": []
    }
    
    # 验证 base64
    try:
        decoded_data = base64.b64decode(base64_data, validate=True)
        diagnosis["is_valid_base64"] = True
        diagnosis["decoded_data"] = decoded_data
    except Exception as e:
        diagnosis["issues"].append(f"Invalid base64: {e}")
        return diagnosis
    
    # 分析 ServeV3 数据
    diagnosis["serve_v3_analysis"] = analyze_serve_v3_data(decoded_data)
    
    return diagnosis


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="ServeV3 数据诊断工具",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  # 诊断 base64 编码的数据
  python diagnose_serve_v3.py --base64 "Q29sbGFwc2VTaWduYXR1cmU..."
  
  # 诊断文件中的数据
  python diagnose_serve_v3.py --file encrypted_data.txt
  
  # 诊断十六进制数据
  python diagnose_serve_v3.py --hex "636f6c6c61707365..."
        """
    )
    
    group = parser.add_mutually_exclusive_group(required=True)
    group.add_argument('--base64', help='Base64 编码的数据')
    group.add_argument('--file', help='包含数据的文件路径')
    group.add_argument('--hex', help='十六进制编码的数据')
    
    parser.add_argument('--verbose', '-v', action='store_true',
                       help='显示详细信息')
    
    args = parser.parse_args()
    
    try:
        # 获取数据
        if args.base64:
            print("🔍 诊断 Base64 数据")
            print("=" * 50)
            diagnosis = diagnose_base64_data(args.base64)
            
            print(f"Base64 长度: {diagnosis['base64_length']}")
            print(f"Base64 有效: {diagnosis['is_valid_base64']}")
            
            if diagnosis['is_valid_base64']:
                decoded_data = diagnosis['decoded_data']
                print(f"解码后长度: {len(decoded_data)} 字节")
                
                if args.verbose:
                    print(f"\n十六进制转储:")
                    print(hex_dump(decoded_data))
                
                # ServeV3 分析
                analysis = diagnosis['serve_v3_analysis']
                print(f"\n📋 ServeV3 分析:")
                print(f"   总长度: {analysis['total_length']} 字节")
                print(f"   长度有效: {analysis['is_valid_length']}")
                print(f"   签名: {analysis['signature']}")
                print(f"   签名有效: {analysis['is_valid_signature']}")
                
                if analysis['is_valid_length']:
                    print(f"   属性: {analysis['attributes']}")
                    print(f"   压缩类型: {analysis['compression_type']}")
                    print(f"   是否加密: {analysis['is_encrypted']}")
                    print(f"   压缩大小: {analysis['compressed_size']}")
                    print(f"   解压大小: {analysis['decompressed_size']}")
                
                if analysis['issues']:
                    print(f"\n❌ 发现问题:")
                    for issue in analysis['issues']:
                        print(f"   • {issue}")
                else:
                    print(f"\n✅ 数据格式看起来正常")
            
            else:
                print(f"\n❌ Base64 解码问题:")
                for issue in diagnosis['issues']:
                    print(f"   • {issue}")
        
        elif args.file:
            print(f"🔍 诊断文件: {args.file}")
            print("=" * 50)
            
            with open(args.file, 'r', encoding='utf-8') as f:
                content = f.read().strip()
            
            # 尝试作为 base64 处理
            diagnosis = diagnose_base64_data(content)
            
            print(f"文件大小: {len(content)} 字符")
            print(f"Base64 有效: {diagnosis['is_valid_base64']}")
            
            if diagnosis['is_valid_base64']:
                decoded_data = diagnosis['decoded_data']
                print(f"解码后长度: {len(decoded_data)} 字节")
                
                # ServeV3 分析
                analysis = diagnosis['serve_v3_analysis']
                print(f"\n📋 ServeV3 分析:")
                print(f"   签名有效: {analysis['is_valid_signature']}")
                print(f"   压缩类型: {analysis['compression_type']}")
                print(f"   是否加密: {analysis['is_encrypted']}")
                print(f"   压缩大小: {analysis['compressed_size']}")
                print(f"   解压大小: {analysis['decompressed_size']}")
                
                if analysis['issues']:
                    print(f"\n❌ 发现问题:")
                    for issue in analysis['issues']:
                        print(f"   • {issue}")
            else:
                print(f"❌ 不是有效的 base64 数据")
        
        elif args.hex:
            print("🔍 诊断十六进制数据")
            print("=" * 50)
            
            try:
                data = bytes.fromhex(args.hex.replace(' ', ''))
                print(f"十六进制长度: {len(args.hex)} 字符")
                print(f"解码后长度: {len(data)} 字节")
                
                analysis = analyze_serve_v3_data(data)
                print(f"\n📋 ServeV3 分析:")
                print(f"   签名有效: {analysis['is_valid_signature']}")
                
                if analysis['is_valid_length']:
                    print(f"   压缩类型: {analysis['compression_type']}")
                    print(f"   是否加密: {analysis['is_encrypted']}")
                    print(f"   压缩大小: {analysis['compressed_size']}")
                    print(f"   解压大小: {analysis['decompressed_size']}")
                
                if analysis['issues']:
                    print(f"\n❌ 发现问题:")
                    for issue in analysis['issues']:
                        print(f"   • {issue}")
                
                if args.verbose:
                    print(f"\n十六进制转储:")
                    print(hex_dump(data))
                    
            except ValueError as e:
                print(f"❌ 无效的十六进制数据: {e}")
        
        return 0
        
    except Exception as e:
        print(f"❌ 错误: {e}")
        return 1


if __name__ == "__main__":
    exit(main())
