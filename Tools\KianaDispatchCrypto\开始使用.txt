KianaDispatch 加密解密工具
===========================

这是一个用于加密和解密 miHoYo 游戏 KianaDispatch 响应的工具。

Windows 用户快速开始：
=====================

1. 【首次使用】双击运行: 快速测试.bat
   - 自动检查 Python 环境
   - 安装必要的依赖包
   - 运行完整测试套件

2. 【解密示例】双击运行: 解密示例数据.bat
   - 解密您提供的实际 dispatch 响应数据
   - 查看解密后的 JSON 结构

3. 【交互使用】双击运行: run_crypto.bat
   - 提供友好的菜单界面
   - 支持加密、解密、自动检测等功能

文件说明：
=========

核心文件：
- kiana_dispatch_crypto.py  # 主要工具（命令行版本）
- test_crypto.py           # 测试脚本
- requirements.txt         # Python 依赖包列表

Windows 批处理文件：
- 快速测试.bat            # 环境检查和测试
- 解密示例数据.bat        # 解密您提供的数据
- 测试东南亚服.bat        # 测试崩坏3东南亚服
- run_crypto.bat          # 交互式界面

文档：
- 工具说明.md             # 中文详细说明
- README.md               # 英文详细文档
- QUICKSTART.md           # 快速开始指南

示例文件：
- examples/sample_encrypted_response.txt  # 示例加密响应
- examples/sample_json_data.json         # 示例 JSON 数据

支持的游戏：
===========
- 崩坏3国服 (Honkai Impact 3rd CN)
- 崩坏3东南亚服 (Honkai Impact 3rd SEA)
- 原神 (Genshin Impact)
- 崩坏：星穹铁道 (Honkai: Star Rail)
- 绝区零 (Zenless Zone Zero)

常用命令示例：
=============

解密国服响应：
python kiana_dispatch_crypto.py decrypt -k bgyzmmhy -v 8.3 -d "加密数据"

获取东南亚服资源：
python game_resource_fetcher.py -k bgyzmmhy -v 8.3 -u "https://osglobal1.honkaiimpact3.com/query_dispatch" -s overseas01

加密 JSON：
python kiana_dispatch_crypto.py encrypt -k bgyzmmhy -v 8.3 -j "JSON数据"

自动检测：
python kiana_dispatch_crypto.py auto -k bgyzmmhy -v 8.3 -d "数据"

需要帮助？
=========
1. 查看 工具说明.md 了解详细用法
2. 运行 快速测试.bat 验证环境
3. 使用 run_crypto.bat 的交互界面
