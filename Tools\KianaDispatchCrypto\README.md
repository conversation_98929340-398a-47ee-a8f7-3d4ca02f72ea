# KianaDispatch Crypto Tool

A Python tool for encrypting and decrypting KianaDispatch responses used by miHoYo games. This tool implements the same encryption/decryption logic used by <PERSON>lapse Launcher's `Hi3Helper.EncTool.Parser.KianaDispatch`.

## Features

- **Decrypt** encrypted dispatch responses from miHoYo game servers
- **Encrypt** JSON data to KianaDispatch format
- **Auto-detect** whether input data is encrypted or plain JSON
- **Command-line interface** for easy integration with scripts
- **File I/O support** for batch processing

## Installation

1. Install Python 3.7 or higher
2. Install dependencies:
   ```bash
   pip install -r requirements.txt
   ```

## Usage

### Basic Commands

#### Decrypt Response
```bash
python kiana_dispatch_crypto.py decrypt -k bgyzmmhy -v 8.3 -d "oG7PvQY0q1ZqrCiH6s+sz1gHfjY8hckVk5vCRUbXBt3v..."
```

#### Encrypt JSON Data
```bash
python kiana_dispatch_crypto.py encrypt -k bgyzmmhy -v 8.3 -j '{"retcode":0,"message":"OK"}'
```

#### Auto-detect and Process
```bash
python kiana_dispatch_crypto.py auto -k bgyzmmhy -v 8.3 -d "some_data"
```

### File Processing

#### Decrypt from File
```bash
python kiana_dispatch_crypto.py decrypt -k bgyzmmhy -v 8.3 -f encrypted_response.txt -o decrypted.json
```

#### Encrypt from File
```bash
python kiana_dispatch_crypto.py encrypt -k bgyzmmhy -v 8.3 -f data.json -o encrypted.txt
```

### Parameters

- `-k, --key`: Dispatcher key from game configuration (e.g., "bgyzmmhy")
- `-v, --version`: Game version in "major.minor" format (e.g., "8.3")
- `-d, --data`: Input data string
- `-j, --json`: JSON data to encrypt
- `-f, --file`: Input file path
- `-o, --output`: Output file path

## How It Works

The encryption/decryption process follows these steps:

### Key Generation
1. **Combine**: Version + DispatcherKey (e.g., "8.3bgyzmmhy")
2. **Hash**: Calculate MD5 hash of the combined string
3. **Convert**: Convert MD5 hash to lowercase hexadecimal
4. **Extract**: Use first 32 bytes as AES-256 key

### Decryption Process
1. **Base64 Decode**: Decode the encrypted response
2. **AES Decrypt**: Use AES-256-ECB to decrypt the data
3. **Remove Padding**: Remove PKCS7 padding if present
4. **Parse JSON**: Convert decrypted bytes to JSON

### Encryption Process
1. **JSON Encode**: Convert data to JSON string
2. **Pad Data**: Add PKCS7 padding to match AES block size
3. **AES Encrypt**: Use AES-256-ECB to encrypt the data
4. **Base64 Encode**: Encode result as Base64 string

## Examples

### Example 1: Decrypt Honkai Impact 3 Response

```bash
# Using the provided encrypted response
python kiana_dispatch_crypto.py decrypt \
  -k bgyzmmhy \
  -v 8.3 \
  -d "oG7PvQY0q1ZqrCiH6s+sz1gHfjY8hckVk5vCRUbXBt3v8/SOWjLo1y4eWPGQDflNFSStynuuBnqex38/pMz94JLVG5uyRlqh25etOw1P+kbKGGFgGd/6hkdq/QJydibSdJq3FvgKyL410DrPMw43Zg3el4XixA8OBEdNHHKyw1VSBfTqLbOwOjj1rFQS+SS3pxZS41sWqLo3uV2W4dX732rEV94xQhyLw6shpgWFCIUofAmss3IRleiM7/yPlwuOjuA77WUnl1GDEW7ZaehglBP3ohmx/KSkmUJfiR/Z1QnCeXFpHQOGp7yI7drrBYSoDxs1/aPQFqmPbdj2vC0wrUx+QTUA1VoQFceCcIvB/gyDa4ym7fiWLTqqJNlelTg0arL1v/Y8SQKZI+fStAiNMtv8MFysHo51r3bnDvsZZRgedoEykDS2TG3v5LyHWajx4fkRupt+fFPijSHjKa7W7qnLRM2s/Cyxty8ROfFqG7MH2/TZ7J5uKuprOgB16uwkTr1Hdx9loRgxt3+FEsy5rnkADtqnnCDZZCynEICqCV8oawHILD7kDStLKWLZWsoqSxbZsJWPtm1RTf4/ZSYRZSTdenuki9U4Q5ClGjX7UuCt8I8Q7+lbbfxIqH8YqDOivyw9zF6HQsmJ8lKoLq4GwPnX5EorAetD9jMfW/hagD3A7cYeQ1t6+OueTaP9H+XxfpkeFqIGUBRl7zsOWPjfdBKLaZ98Kmi0z8JawNjozSDEkV8XgfhWpMJ9o9N18AUzQAp3jVgqwq5BG6M/sx0HVzcu42uvQddlPYVQpv7Zwrto3UxqzKUnGZCzN2j4/QmysOl7T1GquDJhZmvFNWJT2Y0oX17jNW8aWGWJALxjXKGdJmB2F6K7ukIVopJwXUhJyRMLlLFRxbtEOkFAk8ZhyH34hdqpE1XQ6EELQy43Iq2r1eZ5JIuCpEtC+3rRz2nxQ0oyfTPsBRFt5FRgSlcHAM6WRbstfnqpBbLDYCEIHTMaE1/uwfowSWcBAZDgeZrAWuDmvLILCD7lqmV4No7JEQM7O82kgQ4+oUSoniq7eIGdJmB2F6K7ukIVopJwXUhJyRMLlLFRxbtEOkFAk8ZhyB3sT0is1VTpJT33y1hycLFN5GD+S3uNCg1VEIaKNgiSpWWiZN3mPD0HsPAyr+sZGUScJCxy9jGVfmfu8abxBeyTAvWkZF+q2EZpLrqZZyzzHooLJvyfQDzQDGLodFz9EQfb9Nnsnm4q6ms6AHXq7CROvUd3H2WhGDG3f4USzLmuxGrZuEqUYQnOb3MC0dKyCxE8HrUcUeywiTjmpjQf1Ar2htym6MixvuUiqz3SGnWzQy+OkG4VbRluFC7oocJK7TlK1HJ3x/xiXw3+hpdqK1Y="
```

### Example 2: Encrypt Custom Response

```bash
# Create a custom dispatch response
python kiana_dispatch_crypto.py encrypt \
  -k bgyzmmhy \
  -v 8.3 \
  -j '{"retcode":0,"message":"OK","region_list":[{"name":"test","dispatch_url":"https://example.com"}]}'
```

### Example 3: Process Unknown Data

```bash
# Auto-detect if data is encrypted or plain JSON
python kiana_dispatch_crypto.py auto \
  -k bgyzmmhy \
  -v 8.3 \
  -d '{"retcode":0,"message":"OK"}'
```

## Integration

### Python Script Integration

```python
from kiana_dispatch_crypto import KianaDispatchCrypto

# Initialize crypto utility
crypto = KianaDispatchCrypto("bgyzmmhy", "8.3")

# Decrypt response
encrypted_data = "oG7PvQY0q1ZqrCiH..."
decrypted = crypto.decrypt(encrypted_data)
print(decrypted)

# Encrypt data
json_data = {"retcode": 0, "message": "OK"}
encrypted = crypto.encrypt(json_data)
print(encrypted)

# Auto-process
result = crypto.process_response(some_data)
print(result)
```

## Supported Games

This tool works with dispatch responses from:
- Honkai Impact 3rd
- Genshin Impact  
- Honkai: Star Rail
- Zenless Zone Zero
- Other miHoYo games using the same dispatch encryption

## Security Notes

- The encryption uses AES-256-ECB mode
- Keys are derived from game version and dispatcher key
- This tool is for educational and debugging purposes
- Always respect game terms of service

## License

MIT License - See the main Collapse Launcher project for details.
