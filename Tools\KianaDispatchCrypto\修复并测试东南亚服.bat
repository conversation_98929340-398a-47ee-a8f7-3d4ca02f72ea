@echo off
chcp 65001 >nul
REM 修复后的东南亚服测试
REM ====================

echo 🛠️  崩坏3东南亚服修复测试
echo ========================
echo.

echo 🔍 问题诊断:
echo    ❌ 之前的错误: 使用了国服的 token (a9366694...)
echo    ✅ 修复方案: 使用东南亚服的 token (82cb3ccb...)
echo    ✅ 自动检测: 根据 URL 自动选择正确配置
echo.

echo 📋 正确的东南亚服配置:
echo    调度密钥: bgyzmmhy
echo    分发服务器: osglobal1.honkaiimpact3.com
echo    渠道名称: os_pc
echo    认证令牌: 82cb3ccb0be3ee59d8b51e2139c18914
echo    语言参数: lang=EN
echo.

echo 🔧 选择测试方式:
echo 1. 运行修复后的完整测试
echo 2. 快速验证东南亚服连接
echo 3. 对比国服和东南亚服
echo 4. 手动指定配置测试
echo 5. 退出
echo.

:menu
set /p choice="请选择 (1-5): "

if "%choice%"=="1" goto full_test
if "%choice%"=="2" goto quick_test
if "%choice%"=="3" goto compare_test
if "%choice%"=="4" goto manual_test
if "%choice%"=="5" goto exit

echo 无效选择，请重试
goto menu

:full_test
echo.
echo 🧪 运行修复后的完整测试...
echo ===========================
python 修复东南亚服测试.py
echo.
pause
goto menu

:quick_test
echo.
echo ⚡ 快速验证东南亚服连接...
echo =========================
echo 使用自动检测配置测试东南亚服...
echo.

python game_resource_fetcher.py -k bgyzmmhy -v 8.3 -u "https://osglobal1.honkaiimpact3.com/query_dispatch"

if errorlevel 1 (
    echo.
    echo ❌ 测试失败，可能的原因:
    echo    • 网络连接问题
    echo    • 服务器维护中
    echo    • 配置参数错误
) else (
    echo.
    echo ✅ 测试成功！东南亚服连接正常
)
echo.
pause
goto menu

:compare_test
echo.
echo 📊 对比国服和东南亚服...
echo =======================
echo.

echo 🇨🇳 测试国服...
python game_resource_fetcher.py -k bgyzmmhy -v 8.3 -u "https://global1.bh3.com/query_dispatch" -o 国服结果.json

echo.
echo 🌏 测试东南亚服...
python game_resource_fetcher.py -k bgyzmmhy -v 8.3 -u "https://osglobal1.honkaiimpact3.com/query_dispatch" -o 东南亚服结果.json

echo.
echo 📋 对比完成，结果已保存到文件
echo.
pause
goto menu

:manual_test
echo.
echo 🔧 手动配置测试...
echo ================
echo.

set /p custom_url="输入分发服务器 URL (默认东南亚服): "
if "%custom_url%"=="" set custom_url=https://osglobal1.honkaiimpact3.com/query_dispatch

set /p custom_server="输入目标服务器名称 (留空自动检测): "

if "%custom_server%"=="" (
    python game_resource_fetcher.py -k bgyzmmhy -v 8.3 -u "%custom_url%"
) else (
    python game_resource_fetcher.py -k bgyzmmhy -v 8.3 -u "%custom_url%" -s "%custom_server%"
)

echo.
pause
goto menu

:exit
echo.
echo 📝 修复总结:
echo ===========
echo ✅ 修复了 token 配置问题
echo ✅ 添加了自动检测功能
echo ✅ 支持多语言参数
echo ✅ 智能服务器选择
echo.
echo 🚀 现在可以正常使用:
echo python game_resource_fetcher.py -k bgyzmmhy -v 8.3 -u "https://osglobal1.honkaiimpact3.com/query_dispatch"
echo.
echo 再见！
exit /b 0
