@echo off
chcp 65001 >nul
REM 崩坏3东南亚服测试脚本
REM =====================

echo 🌏 崩坏3东南亚服测试工具
echo ========================
echo.

REM 检查 Python 环境
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误: 未安装 Python 或 Python 不在 PATH 中
    echo 请先运行 "快速测试.bat" 进行环境设置
    pause
    exit /b 1
)

REM 检查依赖
python -c "import aiohttp" >nul 2>&1
if errorlevel 1 (
    echo 正在安装缺失的依赖...
    pip install aiohttp
    if errorlevel 1 (
        echo 错误: 依赖安装失败
        pause
        exit /b 1
    )
)

echo 📋 东南亚服配置信息:
echo    区域: Southeast Asia
echo    调度密钥: bgyzmmhy
echo    分发服务器: osglobal1.honkaiimpact3.com
echo    渠道名称: os_pc
echo    默认网关: overseas01
echo    语言支持: 中文、英文、越南语、泰语、印尼语
echo.

echo 🔧 可用操作:
echo 1. 运行完整测试套件
echo 2. 仅测试加密解密
echo 3. 获取东南亚服资源信息
echo 4. 解密自定义数据
echo 5. 查看配置对比
echo 6. 退出
echo.

:menu
set /p choice="请选择操作 (1-6): "

if "%choice%"=="1" goto full_test
if "%choice%"=="2" goto crypto_test
if "%choice%"=="3" goto resource_fetch
if "%choice%"=="4" goto decrypt_custom
if "%choice%"=="5" goto show_comparison
if "%choice%"=="6" goto exit

echo 无效选择，请重试
goto menu

:full_test
echo.
echo 🧪 运行完整测试套件...
echo ========================
python test_hi3_sea.py
echo.
pause
goto menu

:crypto_test
echo.
echo 🔐 测试加密解密功能...
echo ====================
python -c "
from kiana_dispatch_crypto import KianaDispatchCrypto
import json

crypto = KianaDispatchCrypto('bgyzmmhy', '8.3')
test_data = {'retcode': 0, 'message': 'SEA Test'}

print('原始数据:', json.dumps(test_data))
encrypted = crypto.encrypt(test_data)
print('加密结果:', encrypted[:50] + '...')
decrypted = crypto.decrypt(encrypted)
print('解密结果:', json.dumps(decrypted))
print('一致性检查:', '通过' if decrypted == test_data else '失败')
"
echo.
pause
goto menu

:resource_fetch
echo.
echo 🌐 获取东南亚服资源信息...
echo =========================
echo 注意: 此操作需要网络连接
echo.
set /p confirm="确认继续? (y/N): "
if /i not "%confirm%"=="y" goto menu

python game_resource_fetcher.py -k bgyzmmhy -v 8.3 -u "https://osglobal1.honkaiimpact3.com/query_dispatch" -s overseas01
echo.
pause
goto menu

:decrypt_custom
echo.
echo 🔓 解密自定义数据...
echo ==================
set /p data="请输入要解密的数据: "
if "%data%"=="" (
    echo 未输入数据
    goto menu
)

python kiana_dispatch_crypto.py decrypt -k bgyzmmhy -v 8.3 -d "%data%"
echo.
pause
goto menu

:show_comparison
echo.
echo 📊 东南亚服 vs 国服配置对比
echo ===========================
echo.
echo 配置项                 国服 (Hi3CN)                    东南亚服 (Hi3SEA)
echo -----------------------------------------------------------------------
echo 区域                   Mainland China                  Southeast Asia
echo 调度密钥               bgyzmmhy                        bgyzmmhy
echo 分发服务器             global1.bh3.com                 osglobal1.honkaiimpact3.com
echo 渠道名称               gf_pc                           os_pc
echo 默认网关               pc01                            overseas01
echo 认证令牌               a9366694c30364d479dd142f9cf0de54 82cb3ccb0be3ee59d8b51e2139c18914
echo 语言支持               仅中文                          多语言支持
echo 启动器类型             HoYoPlay                        HoYoPlay
echo.
echo 🔧 工具兼容性:
echo ✓ 使用相同的调度密钥
echo ✓ 使用相同的加密算法
echo ✓ 使用相同的工具
echo ✓ 仅需更改 URL 和参数
echo.
pause
goto menu

:exit
echo 再见！
exit /b 0
