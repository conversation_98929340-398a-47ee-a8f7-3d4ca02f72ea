#!/usr/bin/env python3
"""
测试数据对齐修复
===============

验证修复后的加密工具是否能生成与 Collapse Launcher 兼容的 ServeV3 数据。
"""

import json
import base64
import sys
from pathlib import Path

# 添加当前目录到 Python 路径
sys.path.insert(0, str(Path(__file__).parent))

from crypto_tool import CollapseDecryptor
from generate_config_master import MasterKeyGenerator


def test_data_alignment():
    """测试数据对齐修复"""
    
    print("🧪 测试数据对齐修复")
    print("=" * 50)
    
    # 1. 生成测试用的 master key
    print("1️⃣ 生成测试 Master Key...")
    generator = MasterKeyGenerator()
    config = generator.generate_master_key_config(2048)
    
    # 保存到临时文件
    test_master_key_path = "test_master_key.json"
    generator.save_config(config, test_master_key_path)
    
    # 2. 初始化解密器
    print("\n2️⃣ 初始化解密器...")
    decryptor = CollapseDecryptor(test_master_key_path)
    
    # 3. 测试不同长度的数据
    test_cases = [
        "test",
        "Hello, World!",
        "https://example.com/api/endpoint",
        "mihoyo",
        "os_pc",
        "A" * 50,   # 中等长度
        "B" * 100,  # 长数据
        json.dumps({"key": "value", "number": 123}),
        "特殊字符测试: 中文, 日本語, 한국어",
    ]
    
    print(f"\n3️⃣ 测试 {len(test_cases)} 个测试用例...")
    
    success_count = 0
    total_count = len(test_cases)
    
    for i, test_data in enumerate(test_cases, 1):
        print(f"\n测试用例 {i}: {test_data[:30]}{'...' if len(test_data) > 30 else ''}")
        
        try:
            # 加密
            encrypted = decryptor.encrypt_field(test_data)
            print(f"  ✓ 加密成功: {len(encrypted)} 字符")
            
            # 分析 ServeV3 格式
            decoded_data = base64.b64decode(encrypted)
            
            # 检查签名
            signature = int.from_bytes(decoded_data[:8], byteorder='little')
            expected_signature = 7310310183885631299
            if signature != expected_signature:
                print(f"  ❌ 签名错误: {signature}")
                continue
            
            # 读取头部信息
            compressed_size = int.from_bytes(decoded_data[16:24], byteorder='little')
            decompressed_size = int.from_bytes(decoded_data[24:32], byteorder='little')
            attrib_number = int.from_bytes(decoded_data[8:16], byteorder='little')
            compression_type = attrib_number & 0xFF
            is_encrypted = (attrib_number >> 8) & 0xFF == 1
            
            print(f"  📋 ServeV3 信息:")
            print(f"     压缩大小: {compressed_size}")
            print(f"     解压大小: {decompressed_size}")
            print(f"     压缩类型: {compression_type}")
            print(f"     是否加密: {is_encrypted}")
            
            # 模拟 Collapse Launcher 的对齐检查
            data_raw_buffer = decoded_data[32:]
            
            if is_encrypted:
                # 模拟解密过程
                enc_bit_length = config['BitSize']
                
                # 检查加密数据是否能被 BitSize 整除
                if len(data_raw_buffer) % enc_bit_length != 0:
                    print(f"  ❌ 加密数据长度 ({len(data_raw_buffer)}) 不能被 BitSize ({enc_bit_length}) 整除")
                    continue
                
                print(f"  ✓ 加密数据对齐正确")
                
                # 模拟解密后的长度检查
                # 这里我们不实际解密，而是检查理论上的解密长度
                num_chunks = len(data_raw_buffer) // enc_bit_length
                max_decrypted_per_chunk = enc_bit_length - 11  # PKCS1v15 overhead
                max_total_decrypted = num_chunks * max_decrypted_per_chunk
                
                print(f"  📊 解密分析:")
                print(f"     加密块数: {num_chunks}")
                print(f"     每块最大解密: {max_decrypted_per_chunk}")
                print(f"     理论最大解密: {max_total_decrypted}")
                
                if compressed_size > max_total_decrypted:
                    print(f"  ⚠️  压缩大小 ({compressed_size}) 超过理论最大解密长度 ({max_total_decrypted})")
                else:
                    print(f"  ✓ 压缩大小在合理范围内")
            
            # 尝试解密验证
            try:
                decrypted = decryptor.decrypt_field(encrypted)
                if decrypted == test_data:
                    print(f"  ✅ 解密验证成功")
                    success_count += 1
                else:
                    print(f"  ❌ 解密结果不匹配")
                    print(f"     期望: {test_data}")
                    print(f"     实际: {decrypted}")
            except Exception as e:
                print(f"  ❌ 解密失败: {e}")
                
        except Exception as e:
            print(f"  ❌ 测试失败: {e}")
    
    # 4. 生成测试报告
    print(f"\n📊 测试结果:")
    print(f"   总测试数: {total_count}")
    print(f"   成功数: {success_count}")
    print(f"   失败数: {total_count - success_count}")
    print(f"   成功率: {success_count / total_count * 100:.1f}%")
    
    if success_count == total_count:
        print(f"\n🎉 所有测试通过！数据对齐问题已修复")
        return True
    else:
        print(f"\n❌ 部分测试失败，需要进一步调试")
        return False


def test_specific_alignment_scenario():
    """测试特定的对齐场景"""
    
    print("\n🔬 测试特定对齐场景")
    print("=" * 50)
    
    # 生成测试配置
    generator = MasterKeyGenerator()
    config = generator.generate_master_key_config(2048)
    test_master_key_path = "test_master_key_specific.json"
    generator.save_config(config, test_master_key_path)
    
    decryptor = CollapseDecryptor(test_master_key_path)
    
    # 测试不同压缩类型
    test_data = "Hello, Collapse Launcher! This is a test message for alignment verification."
    
    compression_types = [0, 1]  # None, Brotli
    
    for compression_type in compression_types:
        print(f"\n测试压缩类型 {compression_type}:")
        
        try:
            # 手动创建 ServeV3 数据
            data_bytes = test_data.encode('utf-8')
            serve_v3_data = decryptor._create_serve_v3_data(
                data_bytes, 
                use_encryption=True, 
                compression_type=compression_type
            )
            
            # 编码为 base64
            encrypted = base64.b64encode(serve_v3_data).decode('utf-8')
            
            print(f"  ✓ ServeV3 数据创建成功")
            
            # 尝试解密
            decrypted = decryptor.decrypt_field(encrypted)
            
            if decrypted == test_data:
                print(f"  ✅ 解密验证成功")
            else:
                print(f"  ❌ 解密验证失败")
                
        except Exception as e:
            print(f"  ❌ 测试失败: {e}")
            import traceback
            traceback.print_exc()


def main():
    """主函数"""
    
    try:
        # 测试基本对齐修复
        basic_success = test_data_alignment()
        
        # 测试特定场景
        test_specific_alignment_scenario()
        
        # 清理临时文件
        for temp_file in ["test_master_key.json", "test_master_key_specific.json"]:
            try:
                Path(temp_file).unlink()
            except FileNotFoundError:
                pass
        
        return 0 if basic_success else 1
        
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    exit(main())
