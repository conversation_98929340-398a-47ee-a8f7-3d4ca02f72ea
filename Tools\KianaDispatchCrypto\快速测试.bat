@echo off
chcp 65001 >nul
REM KianaDispatch 加密解密工具 - 快速测试
REM ========================================

echo KianaDispatch 加密解密工具 - 快速测试
echo =====================================
echo.

REM 检查 Python 是否已安装
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误: 未安装 Python 或 Python 不在 PATH 中
    echo 请从 https://python.org 安装 Python 3.7 或更高版本
    pause
    exit /b 1
)

echo ✓ Python 已安装
python --version

REM 检查依赖是否已安装
python -c "import Crypto" >nul 2>&1
if errorlevel 1 (
    echo.
    echo 正在安装依赖包...
    pip install -r requirements.txt
    if errorlevel 1 (
        echo 错误: 依赖包安装失败
        pause
        exit /b 1
    )
    echo ✓ 依赖包安装成功
) else (
    echo ✓ 依赖包已安装
)

echo.
echo 开始运行测试...
echo ================
echo.

REM 运行测试套件
python test_crypto.py

echo.
echo ================
echo 测试完成！
echo.

echo 现在您可以尝试以下操作：
echo.
echo 1. 解密示例响应:
echo    python kiana_dispatch_crypto.py decrypt -k bgyzmmhy -v 8.3 -f examples\sample_encrypted_response.txt
echo.
echo 2. 加密示例 JSON:
echo    python kiana_dispatch_crypto.py encrypt -k bgyzmmhy -v 8.3 -f examples\sample_json_data.json
echo.
echo 3. 运行交互式界面:
echo    run_crypto.bat
echo.
echo 4. 解密您提供的实际数据:
echo    python kiana_dispatch_crypto.py decrypt -k bgyzmmhy -v 8.3 -d "oG7PvQY0q1ZqrCiH..."
echo.

pause
