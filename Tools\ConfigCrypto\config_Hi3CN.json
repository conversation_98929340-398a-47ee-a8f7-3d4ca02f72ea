{"ProfileName": "Hi3SEA", "LauncherType": "HoYoPlay", "GameChannel": "Stable", "IsExperimental": false, "ZoneName": "Southeast Asia", "ZoneFullname": "Honkai Impact 3 (Southeast Asia)", "ZoneDescription": "Honkai Impact 3 - In this Honkai-corrupted world, the Valkyries, brave girls with Honkai resistance, have been fighting for all that is beautiful in the world.", "ZoneURL": "https://honkaiimpact3.hoyoverse.com/asia/en-us/fab", "ZoneLogoURL": "metadata/game_logos/logo_honkai.png", "ZonePosterURL": "metadata/game_posters/poster_honkai.png", "InstallRegistryLocation": "HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\Honkai Impact 3", "DefaultGameLocation": "C:\\Program Files\\Honkai Impact 3 sea", "ConfigRegistryLocation": "Software\\miHoYo\\Honkai Impact 3", "BetterHi3LauncherVerInfoReg": "VersionInfoSEA", "FallbackLanguage": "en", "GameDirectoryName": "Games", "GameExecutableName": "BH3.exe", "GameType": "Honkai", "VendorType": "miHoYo", "LauncherID": 9, "ChannelID": 1, "SubChannelID": 1, "GameSupportedLanguages": ["Chinese", "English", "Vietnamese", "Thai", "Indonesian"], "GameDispatchArrayURL": ["Q29sbGFwc2UBAQAAAAAAAC4AAAAAAAAAMgAAAAAAAABg1V3Pg77nt6Qrv74THvE3dQKo33W2yyR2pxnvMGvsUz05CJ3cbjE0Q6hrSJXCKlraY57p/KHrJ3oVKYlWJdEVOgiHubEPwMAyAbtNV5a36qzN1QbHem6lBunzoc3GVvZszPTpEgdqk41WCs7bL0I9S3ZlesogyAX7r/cggJr18g==", "Q29sbGFwc2UBAQAAAAAAAC4AAAAAAAAAMgAAAAAAAACn9LMoyYTZP99IqXSvYmoNlszShVT/HuOe/PVCrj16437kChq5cQkkQ6UBOA8JGcMR0tbtPcpT/jfzqjasHQ5C0zMfiAuSm7Wna5Kl6b1F7q+pnpATRLMUgJj8W/D6l7P54NUZAEZ7igXuSglXk530o2Y2ruP7/N71g6464xOxkg=="], "GameDispatchChannelName": "Q29sbGFwc2UBAQAAAAAAAAkAAAAAAAAABQAAAAAAAABZ0rKQ4n4LwoLcotbwsV5wIZEaQgiWW/5386d+ghQhakpBuC8YFT9A9K+DFKo/oZCNeXgXT+FEP6wSTgrA/q2SkK8NFOM0TVRjnzFxtF9n+vqOxZgRkNfvjwS6SzM4g7CjvzaqmG2iDvnsx2IGVvHzbvFs6C1vOipRGEi80WG/mA==", "GameDispatchURLTemplate": "Q29sbGFwc2UBAQAAAAAAAEkAAAAAAAAARQAAAAAAAAB1MlPVAM4bByAr0759UACGF/LDJTV7vOJRjDGvM+ycGMsXrTisOtedUmm0Q1LeAOnZTgX9umsOWN3Lo6HKlfv/XAOVhlCxkXHUcYMXoPonrFHoTjHZ7tLe5XgOahEX9FscrjQAnm3P9yR8koq/GocSXgM1v563gJ/FXBrVb6Eq9A==", "GameGatewayURLTemplate": "Q29sbGFwc2UBAQAAAAAAAE8AAAAAAAAASwAAAAAAAABjk68ECdLQTR4gvJ16HIwXnhrKVkHpC79BLawx9LkrKJ2VAEyE2dIaychYQ7UScr14gcqcX8VeJzSuMSsXkdPzZd7mws3BolI/NZ9IhLiW8DBVwA0kYAhXAbygVea1Fjsg48PyQa/E4VspJwoqffldnBN14FExqbGRCtjg6jHMVg==", "GameGatewayDefault": "Q29sbGFwc2UBAQAAAAAAAA4AAAAAAAAACgAAAAAAAACP9T4MQJoYrplVp5G0+dg7yNhTNTOMP0v/2BUY6ExnT4PLgbcgtOw5VfS7wlzt9z6p5j9x9V4kinlQP8pj6ff1h/buJZOpqS/n97fgaonup8rAuhz5yepN8MD2wl6wC8svTwOeU3iAlquQ2yFxNSFZIwvw/KVMVTM/IRIw47FdZg==", "GameDefaultCVLanguage": "Japanese", "IsConvertible": true, "IsHideSocMedDesc": false, "ConvertibleTo": ["Global"], "LauncherSpriteURLMultiLang": true, "LauncherSpriteURLMultiLangFallback": "en-us", "LauncherSpriteURL": "Q29sbGFwc2UBAQAAAAAAAGgAAAAAAAAAfwAAAAAAAAAViLH1/MLsr8gMe6tjFWQEMRSVOR1aox/6164blw56MYCX+a+Lopv8VqTbl/b6/N80KHMxj5XBa3BO78SUEWCrimUl1OpIlsfXnDwi+Zbl8vtc0kkNfrJZqp4z3j3DPd7fJwZs0V6avD2W6kz3VArMlxyEB4Rjvx7gNn36NywdyQ==", "LauncherCPSType": "Q29sbGFwc2UBAQAAAAAAAAoAAAAAAAAABgAAAAAAAACnMQc5yO9Jz9NxTdoS0lM/icY73LNfWW5VoHpxYzvjQ9IoeIUBHhDQpH34v8Jw4QGctyI1mqn2Gmy+++VT704r3WzLMCtBwykWTkQ24ySoxFGt1ZLjtk2S20yWLltPljYa9+Oj5VvLcdsDWcj8mMNiZlbuBqFqM9WRFzdQxYdsxA==", "LauncherNewsURL": "Q29sbGFwc2UBAQAAAAAAAGQAAAAAAAAAegAAAAAAAAB3diP5/ZWA6+5cA/7pP0m55wRL91ftVfFX5ELDiEx/hwhRpA5yl5oLP0q7W/CtCthDRMRU45AnhbaVGzyzNpAMCuy6wVRVNz3LZWRWFcUsuChYK6cJ+s9dNt2wvA4C4bDUO5E6aruvsq3t7a5r+UVvq+RbxVTCfrKIx9M4/HG7aA==", "LauncherGameInfoDisplayURL": "Q29sbGFwc2UBAQAAAAAAAGQAAAAAAAAAYQAAAAAAAAAhMHDlG8uAgh9cbtqhwATXdrUtxep1rCGggbThgfwJHQLDRPAtUmv/kpbuRU50IE/8bzlC5ShAZTRapa5XeAqyGjY1HMQZS7QNg3MSiNqIRVvRowPKndj0hwlSQt3Ya/4pK/vVad5hAEnSPdS0J5ZHamBILaCzCnEX1MZn9o6hKQ==", "LauncherResourceURL": "Q29sbGFwc2UBAQAAAAAAAGEAAAAAAAAAcQAAAAAAAAAQdYBvi1t+OIZrEHZGV3arvJt9EL42NOzVQFyStd+U+YvFKDb635Ja4q6REhRlEwGpmXYhrwSOC+HPrN85gb4ARQmNZMZHH33ZhaazSNBqF8XAhjG3yfz1Hli/Kg8RUiwd8kQsn8UIjz+5CSvnWzWUuf6VGAUvhoItJGvS3wOJ7Q==", "LauncherPluginURL": "Q29sbGFwc2UBAQAAAAAAAGIAAAAAAAAAcAAAAAAAAAAlpMbNJRbaCLHWwlXxhtoUboD8z7ldOf4LbUyweSk2WzEISBgX3nq2frbvwJGCMR9tSyJz41dj4yNKCu4EfCtI4aWSik5uAK24Iyvce/KQnc7EMLZS/4PNM/g2FkaikoBWybrmSnRpHMUTjm+UErwKRoxrGQ1ALV/mLP6bgdk1fw==", "LauncherGameChannelSDKURL": "Q29sbGFwc2UBAQAAAAAAAHEAAAAAAAAAjAAAAAAAAABDJ8IZJtnFkvTsnkwSMQxMkTri9FpEpA8Xa0KqQl64ns3VkcvXMNLyZPPTks0VxkGeACO+QKgVt95719A2kTAziiaEy7vHBHEyWX7E5D1pBlKXki+JkFLlJrcM9TysyC3jFwD+aERpKosGnPELCW4Fe/dyxqyo6rXws/2IhLFK2Q==", "LauncherBizName": "Q29sbGFwc2UBAQAAAAAAAA4AAAAAAAAACgAAAAAAAABGzi36wIJHEAJaAt1lfGI1VDR2R5Tpa0k3SLiexYFvmeFtgRcqx2p5RcxkdGzONn8Ma/PbViT65UeQzjwdSGSAUKv938hVnN+T7a2fZoVTuuYgMVQ3aAv8/kdbcMB8jHhZtqxz+oU1+RbXrEqBIwt5az8HBb/0WFuHutCBNICcpA==", "LauncherId": "Q29sbGFwc2UBAQAAAAAAAA4AAAAAAAAACgAAAAAAAABGtmz5Pga0nIJxuVJb7kC/drSlrlhrqf2dXzEkR6QxY0Sg2gvkJ9uWSSnWYLhKzhaJZaYW8nZoCnkMRV1bOhIN754TXSPzXfTYjCXdgs4228UwwIH9A7JrxY8XLcdL2YbeWpc5pIbQ0fAg2ovCrgsnO10z34/pNNRfOG74CDOXLA==", "LauncherGameId": "Q29sbGFwc2UBAQAAAAAAAA4AAAAAAAAACgAAAAAAAAAMEVQQD/UvtYnv4YEXyikjbR2V2WXY2ZT1Pa1Y47w3KqvyAAR8sh/hMwN6TfvnpdAlerixqss4Towwfugqa82Vp2kCh8K0Of+ii8TDj3Od9pLFnj/cYdFmwkgBrP/BDuhmhiyKOwmAghAVq6SZyd9syvoQ7AWgVXwnWdZvmdqehQ==", "DispatcherKey": "Q29sbGFwc2UBAQAAAAAAAAwAAAAAAAAACAAAAAAAAAAxepb5hcPmpGOe0CFrqYGPEJt64iRL5wEj8gKcivFj3ZetAp2MOoR/yZCiIuEIYgJX8lCVrF8P5HB1rBUTDReaXdcAjssGgwUhvfURZ9/30M3+oM3ugpt0wNIK5uRE7A9azNbz8ND/3CX93s49gjvJyUY7uxLoaAq9n68O0XtSKw==", "IsPluginUpdateEnabled": false, "IsRepairEnabled": true, "IsCacheUpdateEnabled": true, "InternalGameNameFolder": "Honkai Impact 3 sea", "InternalGameNameInConfig": "Honkai Impact 3", "GameDataTemplates": {}, "ZoneSteamAssets": {"Logo": {"URL": "metadata/game_logos/logo_honkai.png", "MD5": "cb2661b1f53553ce65cec84435f40b08"}, "Hero": {"URL": "metadata/steam_assets/game_heroes/hero_honkai.png", "MD5": "51c3e1bc9dc3e087684dddaa59555cdb"}, "Banner": {"URL": "metadata/steam_assets/game_banners/banner_honkai.png", "MD5": "a40c78e5c5a26990acf47df79be187ba"}, "Preview": {"URL": "metadata/steam_assets/game_previews/preview_honkai.png", "MD5": "53f3874d5dfba82e737b08cde3d62e9c"}}, "ApiGeneralUserAgent": "Mozilla/5.0", "ApiResourceUserAgent": "HYPContainer/1.3.3.182 (windows {0})", "ApiResourceAdditionalHeaders": {"sec-ch-ua": "\"Chromium\";v=\"102\"", "x-rpc-client_version": "1.3.3.182", "sec-ch-ua-mobile": "?0", "sec-ch-ua-platform": "\"Windows\""}, "Hash": -4245500975932299680}