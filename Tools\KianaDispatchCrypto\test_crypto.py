#!/usr/bin/env python3
"""
Test script for KianaDispatch Crypto Tool
==========================================

This script tests the encryption and decryption functionality
using the provided sample data.
"""

import json
import sys
from kiana_dispatch_crypto import KianaDispatchCrypto


def test_decryption():
    """Test decryption with the provided sample data."""
    print("Testing Decryption...")
    print("=" * 50)
    
    # Sample data from the user
    dispatcher_key = "bgyzmmhy"
    version = "8.3"
    encrypted_data = "oG7PvQY0q1ZqrCiH6s+sz1gHfjY8hckVk5vCRUbXBt3v8/SOWjLo1y4eWPGQDflNFSStynuuBnqex38/pMz94JLVG5uyRlqh25etOw1P+kbKGGFgGd/6hkdq/QJydibSdJq3FvgKyL410DrPMw43Zg3el4XixA8OBEdNHHKyw1VSBfTqLbOwOjj1rFQS+SS3pxZS41sWqLo3uV2W4dX732rEV94xQhyLw6shpgWFCIUofAmss3IRleiM7/yPlwuOjuA77WUnl1GDEW7ZaehglBP3ohmx/KSkmUJfiR/Z1QnCeXFpHQOGp7yI7drrBYSoDxs1/aPQFqmPbdj2vC0wrUx+QTUA1VoQFceCcIvB/gyDa4ym7fiWLTqqJNlelTg0arL1v/Y8SQKZI+fStAiNMtv8MFysHo51r3bnDvsZZRgedoEykDS2TG3v5LyHWajx4fkRupt+fFPijSHjKa7W7qnLRM2s/Cyxty8ROfFqG7MH2/TZ7J5uKuprOgB16uwkTr1Hdx9loRgxt3+FEsy5rnkADtqnnCDZZCynEICqCV8oawHILD7kDStLKWLZWsoqSxbZsJWPtm1RTf4/ZSYRZSTdenuki9U4Q5ClGjX7UuCt8I8Q7+lbbfxIqH8YqDOivyw9zF6HQsmJ8lKoLq4GwPnX5EorAetD9jMfW/hagD3A7cYeQ1t6+OueTaP9H+XxfpkeFqIGUBRl7zsOWPjfdBKLaZ98Kmi0z8JawNjozSDEkV8XgfhWpMJ9o9N18AUzQAp3jVgqwq5BG6M/sx0HVzcu42uvQddlPYVQpv7Zwrto3UxqzKUnGZCzN2j4/QmysOl7T1GquDJhZmvFNWJT2Y0oX17jNW8aWGWJALxjXKGdJmB2F6K7ukIVopJwXUhJyRMLlLFRxbtEOkFAk8ZhyH34hdqpE1XQ6EELQy43Iq2r1eZ5JIuCpEtC+3rRz2nxQ0oyfTPsBRFt5FRgSlcHAM6WRbstfnqpBbLDYCEIHTMaE1/uwfowSWcBAZDgeZrAWuDmvLILCD7lqmV4No7JEQM7O82kgQ4+oUSoniq7eIGdJmB2F6K7ukIVopJwXUhJyRMLlLFRxbtEOkFAk8ZhyB3sT0is1VTpJT33y1hycLFN5GD+S3uNCg1VEIaKNgiSpWWiZN3mPD0HsPAyr+sZGUScJCxy9jGVfmfu8abxBeyTAvWkZF+q2EZpLrqZZyzzHooLJvyfQDzQDGLodFz9EQfb9Nnsnm4q6ms6AHXq7CROvUd3H2WhGDG3f4USzLmuxGrZuEqUYQnOb3MC0dKyCxE8HrUcUeywiTjmpjQf1Ar2htym6MixvuUiqz3SGnWzQy+OkG4VbRluFC7oocJK7TlK1HJ3x/xiXw3+hpdqK1Y="
    
    try:
        # Initialize crypto utility
        crypto = KianaDispatchCrypto(dispatcher_key, version)
        
        # Show key generation process
        print(f"Dispatcher Key: {dispatcher_key}")
        print(f"Game Version: {version}")
        print(f"Combined Key: {version}{dispatcher_key}")
        print(f"Generated AES Key: {crypto._aes_key.hex()}")
        print()
        
        # Decrypt the data
        print("Decrypting response...")
        decrypted_data = crypto.decrypt(encrypted_data)
        
        # Pretty print the result
        print("Decrypted JSON:")
        print(json.dumps(decrypted_data, indent=2, ensure_ascii=False))
        
        return decrypted_data
        
    except Exception as e:
        print(f"Decryption failed: {e}")
        return None


def test_encryption(data_to_encrypt):
    """Test encryption by re-encrypting decrypted data."""
    print("\n" + "=" * 50)
    print("Testing Encryption...")
    print("=" * 50)
    
    if not data_to_encrypt:
        print("No data to encrypt (decryption failed)")
        return None
    
    try:
        # Initialize crypto utility
        crypto = KianaDispatchCrypto("bgyzmmhy", "8.3")
        
        # Encrypt the data
        print("Encrypting JSON data...")
        encrypted_result = crypto.encrypt(data_to_encrypt)
        
        print(f"Encrypted result length: {len(encrypted_result)} characters")
        print(f"Encrypted result (first 100 chars): {encrypted_result[:100]}...")
        
        return encrypted_result
        
    except Exception as e:
        print(f"Encryption failed: {e}")
        return None


def test_round_trip(original_encrypted, re_encrypted):
    """Test round-trip encryption/decryption."""
    print("\n" + "=" * 50)
    print("Testing Round-trip...")
    print("=" * 50)
    
    if not re_encrypted:
        print("No re-encrypted data to test")
        return False
    
    try:
        crypto = KianaDispatchCrypto("bgyzmmhy", "8.3")
        
        # Decrypt the re-encrypted data
        print("Decrypting re-encrypted data...")
        round_trip_data = crypto.decrypt(re_encrypted)
        
        # Decrypt original for comparison
        original_data = crypto.decrypt(original_encrypted)
        
        # Compare results
        if round_trip_data == original_data:
            print("✅ Round-trip test PASSED - Data matches!")
            return True
        else:
            print("❌ Round-trip test FAILED - Data doesn't match!")
            print("Original keys:", list(original_data.keys()) if isinstance(original_data, dict) else "Not a dict")
            print("Round-trip keys:", list(round_trip_data.keys()) if isinstance(round_trip_data, dict) else "Not a dict")
            return False
            
    except Exception as e:
        print(f"Round-trip test failed: {e}")
        return False


def test_auto_detect():
    """Test auto-detection functionality."""
    print("\n" + "=" * 50)
    print("Testing Auto-detection...")
    print("=" * 50)
    
    crypto = KianaDispatchCrypto("bgyzmmhy", "8.3")
    
    # Test with plain JSON
    plain_json = '{"retcode": 0, "message": "OK", "test": true}'
    print("Testing with plain JSON...")
    try:
        result1 = crypto.process_response(plain_json)
        print("✅ Plain JSON processed successfully")
        print(f"Result: {result1}")
    except Exception as e:
        print(f"❌ Plain JSON processing failed: {e}")
    
    # Test with encrypted data
    encrypted_data = "oG7PvQY0q1ZqrCiH6s+sz1gHfjY8hckVk5vCRUbXBt3v8/SOWjLo1y4eWPGQDflNFSStynuuBnqex38/pMz94JLVG5uyRlqh25etOw1P+kb"
    print("\nTesting with encrypted data...")
    try:
        result2 = crypto.process_response(encrypted_data)
        print("✅ Encrypted data processed successfully")
        print(f"Result keys: {list(result2.keys()) if isinstance(result2, dict) else 'Not a dict'}")
    except Exception as e:
        print(f"❌ Encrypted data processing failed: {e}")


def main():
    """Run all tests."""
    print("KianaDispatch Crypto Tool - Test Suite")
    print("=" * 60)
    
    # Test 1: Decryption
    decrypted_data = test_decryption()
    
    # Test 2: Encryption
    re_encrypted_data = test_encryption(decrypted_data)
    
    # Test 3: Round-trip
    original_encrypted = "oG7PvQY0q1ZqrCiH6s+sz1gHfjY8hckVk5vCRUbXBt3v8/SOWjLo1y4eWPGQDflNFSStynuuBnqex38/pMz94JLVG5uyRlqh25etOw1P+kbKGGFgGd/6hkdq/QJydibSdJq3FvgKyL410DrPMw43Zg3el4XixA8OBEdNHHKyw1VSBfTqLbOwOjj1rFQS+SS3pxZS41sWqLo3uV2W4dX732rEV94xQhyLw6shpgWFCIUofAmss3IRleiM7/yPlwuOjuA77WUnl1GDEW7ZaehglBP3ohmx/KSkmUJfiR/Z1QnCeXFpHQOGp7yI7drrBYSoDxs1/aPQFqmPbdj2vC0wrUx+QTUA1VoQFceCcIvB/gyDa4ym7fiWLTqqJNlelTg0arL1v/Y8SQKZI+fStAiNMtv8MFysHo51r3bnDvsZZRgedoEykDS2TG3v5LyHWajx4fkRupt+fFPijSHjKa7W7qnLRM2s/Cyxty8ROfFqG7MH2/TZ7J5uKuprOgB16uwkTr1Hdx9loRgxt3+FEsy5rnkADtqnnCDZZCynEICqCV8oawHILD7kDStLKWLZWsoqSxbZsJWPtm1RTf4/ZSYRZSTdenuki9U4Q5ClGjX7UuCt8I8Q7+lbbfxIqH8YqDOivyw9zF6HQsmJ8lKoLq4GwPnX5EorAetD9jMfW/hagD3A7cYeQ1t6+OueTaP9H+XxfpkeFqIGUBRl7zsOWPjfdBKLaZ98Kmi0z8JawNjozSDEkV8XgfhWpMJ9o9N18AUzQAp3jVgqwq5BG6M/sx0HVzcu42uvQddlPYVQpv7Zwrto3UxqzKUnGZCzN2j4/QmysOl7T1GquDJhZmvFNWJT2Y0oX17jNW8aWGWJALxjXKGdJmB2F6K7ukIVopJwXUhJyRMLlLFRxbtEOkFAk8ZhyH34hdqpE1XQ6EELQy43Iq2r1eZ5JIuCpEtC+3rRz2nxQ0oyfTPsBRFt5FRgSlcHAM6WRbstfnqpBbLDYCEIHTMaE1/uwfowSWcBAZDgeZrAWuDmvLILCD7lqmV4No7JEQM7O82kgQ4+oUSoniq7eIGdJmB2F6K7ukIVopJwXUhJyRMLlLFRxbtEOkFAk8ZhyB3sT0is1VTpJT33y1hycLFN5GD+S3uNCg1VEIaKNgiSpWWiZN3mPD0HsPAyr+sZGUScJCxy9jGVfmfu8abxBeyTAvWkZF+q2EZpLrqZZyzzHooLJvyfQDzQDGLodFz9EQfb9Nnsnm4q6ms6AHXq7CROvUd3H2WhGDG3f4USzLmuxGrZuEqUYQnOb3MC0dKyCxE8HrUcUeywiTjmpjQf1Ar2htym6MixvuUiqz3SGnWzQy+OkG4VbRluFC7oocJK7TlK1HJ3x/xiXw3+hpdqK1Y="
    round_trip_success = test_round_trip(original_encrypted, re_encrypted_data)
    
    # Test 4: Auto-detection
    test_auto_detect()
    
    # Summary
    print("\n" + "=" * 60)
    print("Test Summary:")
    print("=" * 60)
    print(f"✅ Decryption: {'PASSED' if decrypted_data else 'FAILED'}")
    print(f"✅ Encryption: {'PASSED' if re_encrypted_data else 'FAILED'}")
    print(f"✅ Round-trip: {'PASSED' if round_trip_success else 'FAILED'}")
    print("✅ Auto-detection: See results above")
    
    if decrypted_data and re_encrypted_data and round_trip_success:
        print("\n🎉 All core tests PASSED! The tool is working correctly.")
        return 0
    else:
        print("\n❌ Some tests FAILED. Please check the implementation.")
        return 1


if __name__ == '__main__':
    sys.exit(main())
