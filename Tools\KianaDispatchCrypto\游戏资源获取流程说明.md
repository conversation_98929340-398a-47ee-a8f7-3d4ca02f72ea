# 游戏资源获取流程详细说明

## 概述

本文档详细说明了从解密的 dispatch 响应到获取游戏安装包和更新补丁的完整流程，基于 Collapse Launcher 的实现。

## 完整流程图

```mermaid
graph TD
    A[Dispatch URL] --> B[加密响应]
    B --> C[解密工具]
    C --> D[服务器列表]
    D --> E[选择目标服务器]
    E --> F[Gameserver URL]
    F --> G[加密的游戏服务器响应]
    G --> H[解密工具]
    H --> I[资源 URL 列表]
    I --> J{安装方式}
    J -->|传统方式| K[pkg_version 文件]
    J -->|Sophon 方式| L[Manifest API]
    K --> M[文件列表]
    L --> N[分块信息]
    M --> O[下载游戏文件]
    N --> P[下载分块文件]
    O --> Q[安装游戏]
    P --> Q
```

## 详细步骤说明

### 第一步：解密 Dispatch 响应

**输入：** 您提供的加密响应数据
```
oG7PvQY0q1ZqrCiH6s+sz1gHfjY8hckVk5vCRUbXBt3v8/SOWjLo1y4eWPGQDflN...
```

**处理：** 使用 KianaDispatch 解密算法
- 密钥生成：`8.3` + `bgyzmmhy` → MD5 → 十六进制 → AES-256 密钥
- 解密：Base64 解码 → AES-256-ECB 解密 → JSON 解析

**输出：** 服务器列表
```json
{
  "retcode": 0,
  "region_list": [
    {"name": "pc01", "title": "pc01", "dispatch_url": "https://outer-dp-pc01.bh3.com/query_gameserver"},
    {"name": "android01", "title": "安卓国服", "dispatch_url": "https://outer-dp-android01.bh3.com/query_gameserver"},
    // ... 其他服务器
  ]
}
```

### 第二步：选择目标服务器

**选择逻辑：**
- 根据配置中的 `GameGatewayDefault`（通常是 "pc01"）
- 或根据用户偏好选择特定服务器
- 推荐服务器通常标记为 `"ext": {"1794460900": "Recommend"}`

**选中服务器：** pc01
- 名称：pc01
- 标题：pc01
- URL：https://outer-dp-pc01.bh3.com/query_gameserver

### 第三步：查询游戏服务器

**构建 Gameserver URL：**
```
https://outer-dp-pc01.bh3.com/query_gameserver?version=8.3.0_gf_pc&t=1703123456&token=a9366694c30364d479dd142f9cf0de54
```

**参数说明：**
- `version`: 完整版本号_渠道名称
- `t`: Unix 时间戳
- `token`: 认证令牌

**响应内容：** 加密的游戏服务器信息

### 第四步：解密游戏服务器响应

**使用相同的解密设置：**
- 调度密钥：bgyzmmhy
- 游戏版本：8.3

**解密后的响应示例：**
```json
{
  "retcode": 0,
  "is_data_ready": true,
  "asset_bundle_url_list": [
    "https://autopatchcn.bh3.com/ptpublic/rel/20241201_6.9.0_3572/output_6398313_e267eb1b9b"
  ],
  "ex_resource_url_list": [
    "https://autopatchcn.bh3.com/ptpublic/rel/20241201_6.9.0_3572/output_6398313_e267eb1b9b"
  ]
}
```

### 第五步：构建资源下载 URL

**基础 URL：**
```
https://autopatchcn.bh3.com/ptpublic/rel/20241201_6.9.0_3572/output_6398313_e267eb1b9b
```

**各种资源 URL：**

#### 1. pkg_version 文件（传统安装）
```
https://autopatchcn.bh3.com/ptpublic/rel/20241201_6.9.0_3572/output_6398313_e267eb1b9b/pkg_version
```

#### 2. 缓存资源文件
```
# 数据缓存
https://autopatchcn.bh3.com/ptpublic/rel/20241201_6.9.0_3572/output_6398313_e267eb1b9b/data/editor_compressed/DataVersion.unity3d

# 事件缓存
https://autopatchcn.bh3.com/ptpublic/rel/20241201_6.9.0_3572/output_6398313_e267eb1b9b/event/editor_compressed/ResourceVersion.unity3d

# AI 缓存
https://autopatchcn.bh3.com/ptpublic/rel/20241201_6.9.0_3572/output_6398313_e267eb1b9b/ai/editor_compressed/ResourceVersion.unity3d
```

### 第六步：获取安装包信息

#### 方式一：传统 pkg_version 方式

**下载 pkg_version 文件：**
```bash
curl "https://autopatchcn.bh3.com/ptpublic/rel/20241201_6.9.0_3572/output_6398313_e267eb1b9b/pkg_version"
```

**pkg_version 内容格式：**
```json
{
  "remoteName": "BH3_Data/StreamingAssets/AssetBundles/blocks/00/00000000.unity3d",
  "md5": "d41d8cd98f00b204e9800998ecf8427e",
  "fileSize": 1024
}
```

#### 方式二：Sophon 分块方式

**使用配置中的 Sophon URL：**
```json
{
  "MainUrl": "https://api-takumi.mihoyo.com/downloader/sophon_chunk/api/getBuild?branch=main&package_id=jdEaAE9pKa&password=h6XjD8ReKlR1",
  "PreloadUrl": "https://api-takumi.mihoyo.com/downloader/sophon_chunk/api/getBuild?branch=predownload&package_id=jdEaAE9pKa&password=OAvMeb9icV0x",
  "PatchUrl": "https://api-takumi.mihoyo.com/downloader/sophon_chunk/api/getPatchBuild?branch=main&password=h6XjD8ReKlR1&package_id=jdEaAE9pKa"
}
```

**Sophon 响应格式：**
```json
{
  "retcode": 0,
  "data": {
    "build_id": "6H8QvYqRWaj3",
    "tag": "8.3.0",
    "manifests": [{
      "category_name": "游戏资源-外网",
      "chunk_download": {
        "url_prefix": "https://autopatchcn.bh3.com/ptpublic/rel/chunk/chunks/...",
        "compression": 1
      },
      "stats": {
        "file_count": "944",
        "chunk_count": "25444",
        "compressed_size": "26670056736"
      }
    }]
  }
}
```

### 第七步：下载和安装

#### 传统安装流程

1. **解析 pkg_version**：获取所有需要下载的文件列表
2. **下载文件**：根据文件列表逐个下载
3. **验证完整性**：使用 MD5 校验文件
4. **解压安装**：将文件解压到游戏目录

#### Sophon 安装流程

1. **获取 Manifest**：从 Sophon API 获取文件分块信息
2. **并行下载**：同时下载多个分块文件
3. **重组文件**：将分块重新组合成完整文件
4. **验证安装**：校验文件完整性并安装

### 第八步：更新补丁获取

#### 增量更新检查

**比较版本：**
- 本地版本：从游戏目录读取
- 服务器版本：从 API 响应获取
- 如果版本不同，需要更新

#### 补丁下载

**传统方式：**
- 下载新版本的 pkg_version
- 比较文件差异
- 只下载变更的文件

**Sophon 方式：**
- 使用 PatchUrl 获取补丁信息
- 下载增量分块
- 应用补丁到现有文件

## 工具使用示例

### 1. 解密您的 dispatch 响应

```bash
python kiana_dispatch_crypto.py decrypt -k bgyzmmhy -v 8.3 -d "oG7PvQY0q1ZqrCiH..."
```

### 2. 获取游戏服务器信息

```bash
python kiana_dispatch_crypto.py gameserver -k bgyzmmhy -v 8.3 -d "解密后的dispatch数据" -s pc01
```

### 3. 获取完整资源信息

```bash
python game_resource_fetcher.py -k bgyzmmhy -v 8.3 -u "https://global1.bh3.com/query_dispatch" -s pc01
```

### 4. 演示完整流程

```bash
python 演示完整流程.py
```

## 总结

从您的解密结果到获取游戏安装包的完整流程包括：

1. ✅ **解密 dispatch 响应** → 获取服务器列表
2. ✅ **选择目标服务器** → 通常是 pc01
3. ✅ **查询游戏服务器** → 获取资源 URL
4. ✅ **解密服务器响应** → 提取下载地址
5. ✅ **构建下载 URL** → pkg_version 和缓存文件
6. ✅ **获取文件列表** → 传统或 Sophon 方式
7. ✅ **下载安装包** → 并行下载和验证
8. ✅ **安装游戏** → 解压到目标目录

这个流程确保了：
- 🔒 **安全性**：所有通信都经过加密
- 🚀 **效率**：支持并行下载和断点续传
- 🎯 **准确性**：完整性验证确保文件正确
- 🔄 **可靠性**：支持重试和错误恢复

您现在可以使用提供的工具来实现完整的游戏资源获取和安装流程！
