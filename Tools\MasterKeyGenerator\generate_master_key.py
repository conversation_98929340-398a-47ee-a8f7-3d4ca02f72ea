#!/usr/bin/env python3
"""
CollapseLauncher Master Key Generator

This tool generates master key configuration files and stamp entries
for CollapseLauncher metadata system.
"""

import argparse
import base64
import json
import os
import time
from datetime import datetime
from pathlib import Path
from typing import Dict, Any

from cryptography.hazmat.primitives import serialization, hashes
from cryptography.hazmat.primitives.asymmetric import rsa
from cryptography.hazmat.backends import default_backend


class MasterKeyGenerator:
    """Generator for CollapseLauncher master key configuration."""
    
    def __init__(self, key_size: int = 1024, bit_size: int = 128):
        """
        Initialize the generator.
        
        Args:
            key_size: RSA key size in bits
            bit_size: BitSize field value for the config
        """
        self.key_size = key_size
        self.bit_size = bit_size
        self.private_key = None
        self.public_key = None
        
    def generate_rsa_keypair(self) -> None:
        """Generate RSA key pair."""
        print(f"Generating RSA key pair with {self.key_size} bits...")
        
        # Generate private key
        self.private_key = rsa.generate_private_key(
            public_exponent=65537,
            key_size=self.key_size,
            backend=default_backend()
        )
        
        # Get public key
        self.public_key = self.private_key.public_key()
        
        print("RSA key pair generated successfully.")
    
    def create_collapse_key_format(self, use_serve_v3: bool = True) -> str:
        """
        Create the master key in the correct format.

        Args:
            use_serve_v3: Whether to wrap the key in ServeV3 format

        Returns:
            Base64 encoded key data (DER format or ServeV3 wrapped)
        """
        if not self.private_key:
            raise ValueError("Private key not generated. Call generate_rsa_keypair() first.")

        # Serialize private key to DER format (same as C# ImportRSAPrivateKey expects)
        der_private = self.private_key.private_bytes(
            encoding=serialization.Encoding.DER,
            format=serialization.PrivateFormat.PKCS8,
            encryption_algorithm=serialization.NoEncryption()
        )

        if use_serve_v3:
            # Wrap in ServeV3 format
            serve_v3_data = self._create_serve_v3_data(der_private)
            return base64.b64encode(serve_v3_data).decode('utf-8')
        else:
            # Direct DER format
            return base64.b64encode(der_private).decode('utf-8')

    def _create_serve_v3_data(self, data: bytes, use_encryption: bool = False,
                             compression_type: int = 0) -> bytes:
        """
        Create ServeV3 format data for master key.

        Args:
            data: DER format RSA private key data
            use_encryption: Whether to encrypt (False for master key)
            compression_type: 0=None, 1=Brotli, 2=Zstd

        Returns:
            ServeV3 format data
        """
        processed_data = data

        # Apply compression if requested
        if compression_type == 1:  # Brotli
            try:
                import brotli
                processed_data = brotli.compress(processed_data)
            except ImportError:
                print("Warning: brotli not available, using no compression")
                compression_type = 0
        elif compression_type == 2:  # Zstd
            try:
                import zstandard as zstd
                cctx = zstd.ZstdCompressor()
                processed_data = cctx.compress(processed_data)
            except ImportError:
                print("Warning: zstandard not available, using no compression")
                compression_type = 0

        # Master key is not encrypted (use_encryption = False)
        # The master key itself is used to encrypt other data

        # Create ServeV3 header
        collapse_signature = 7310310183885631299

        # Attributes: compression_type + (is_use_encryption << 8)
        attributes = compression_type | ((1 if use_encryption else 0) << 8)

        compressed_size = len(processed_data)
        decompressed_size = len(data)

        # Build header (32 bytes)
        header = bytearray(32)
        header[0:8] = collapse_signature.to_bytes(8, byteorder='little')
        header[8:16] = attributes.to_bytes(8, byteorder='little')
        header[16:24] = compressed_size.to_bytes(8, byteorder='little')
        header[24:32] = decompressed_size.to_bytes(8, byteorder='little')

        # Combine header and data
        return bytes(header) + processed_data


    
    def calculate_hash(self, key_data: str) -> int:
        """
        Calculate hash for the key data.
        
        Args:
            key_data: Base64 encoded key data
            
        Returns:
            Hash value as integer
        """
        # Simple hash calculation (mimicking CRC32-like behavior)
        key_bytes = key_data.encode('utf-8')
        hash_value = 0
        
        for byte in key_bytes:
            hash_value = ((hash_value << 5) + hash_value + byte) & 0xFFFFFFFFFFFFFFFF
        
        # Convert to signed 64-bit integer
        if hash_value >= 2**63:
            hash_value -= 2**64
            
        return hash_value
    
    def create_master_key_config(self, use_serve_v3: bool = True) -> Dict[str, Any]:
        """
        Create the master key configuration dictionary.

        Args:
            use_serve_v3: Whether to use ServeV3 format for the key

        Returns:
            Dictionary representing the config_master.json content
        """
        if not self.private_key:
            raise ValueError("Private key not generated. Call generate_rsa_keypair() first.")

        # Create the key in the specified format
        key_data = self.create_collapse_key_format(use_serve_v3)

        # Calculate hash
        hash_value = self.calculate_hash(key_data)

        return {
            "Key": key_data,
            "BitSize": self.bit_size,
            "Hash": hash_value
        }
    
    def create_stamp_entry(self, config_version: str = "3.1.0") -> Dict[str, Any]:
        """
        Create the stamp entry for the master key.
        
        Args:
            config_version: Version string for the configuration
            
        Returns:
            Dictionary representing the stamp entry
        """
        # Generate timestamp (current time in format: YYYYMMDDHHMMSS)
        timestamp = int(datetime.now().strftime("%Y%m%d%H%M%S"))
        
        return {
            "LastUpdated": timestamp,
            "MetadataPath": "config_master.json",
            "MetadataType": "MasterKey",
            "MetadataInclude": True,
            "PresetConfigVersion": config_version
        }
    
    def save_files(self, output_dir: str, config_version: str = "3.1.0",
                   use_serve_v3: bool = True) -> None:
        """
        Save all generated files to the output directory.

        Args:
            output_dir: Directory to save files
            config_version: Version string for the configuration
            use_serve_v3: Whether to use ServeV3 format for the key
        """
        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)

        print(f"Saving files to: {output_path.absolute()}")
        print(f"Using ServeV3 format: {use_serve_v3}")

        # Generate configurations
        master_config = self.create_master_key_config(use_serve_v3)
        stamp_entry = self.create_stamp_entry(config_version)
        
        # Save config_master.json
        config_file = output_path / "config_master.json"
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(master_config, f, indent=2, ensure_ascii=False)
        print(f"✓ Saved: {config_file}")
        
        # Save stamp entry
        stamp_file = output_path / "stamp_entry.json"
        with open(stamp_file, 'w', encoding='utf-8') as f:
            json.dump(stamp_entry, f, indent=2, ensure_ascii=False)
        print(f"✓ Saved: {stamp_file}")
        
        # Save private key (for backup)
        private_key_file = output_path / "private_key.pem"
        with open(private_key_file, 'wb') as f:
            f.write(self.private_key.private_bytes(
                encoding=serialization.Encoding.PEM,
                format=serialization.PrivateFormat.PKCS8,
                encryption_algorithm=serialization.NoEncryption()
            ))
        print(f"✓ Saved: {private_key_file}")
        
        # Save public key (for verification)
        public_key_file = output_path / "public_key.pem"
        with open(public_key_file, 'wb') as f:
            f.write(self.public_key.public_bytes(
                encoding=serialization.Encoding.PEM,
                format=serialization.PublicFormat.SubjectPublicKeyInfo
            ))
        print(f"✓ Saved: {public_key_file}")
        
        print("\n" + "="*50)
        print("Master Key Generation Complete!")
        print("="*50)
        print(f"Key Size: {self.key_size} bits")
        print(f"Bit Size: {self.bit_size}")
        print(f"Hash: {master_config['Hash']}")
        print(f"Timestamp: {stamp_entry['LastUpdated']}")
        print("="*50)


def main():
    """Main function to handle command line arguments and run the generator."""
    parser = argparse.ArgumentParser(
        description="Generate CollapseLauncher master key configuration files"
    )
    
    parser.add_argument(
        '--key-size',
        type=int,
        default=1024,
        help='RSA key size in bits (default: 1024)'
    )
    
    parser.add_argument(
        '--bit-size',
        type=int,
        default=128,
        help='BitSize field value (default: 128)'
    )
    
    parser.add_argument(
        '--output-dir',
        type=str,
        default='./output',
        help='Output directory (default: ./output)'
    )
    
    parser.add_argument(
        '--config-version',
        type=str,
        default='3.1.0',
        help='Configuration version (default: 3.1.0)'
    )

    parser.add_argument(
        '--use-serve-v3',
        action='store_true',
        default=True,
        help='Use ServeV3 format for master key (default: True)'
    )

    parser.add_argument(
        '--no-serve-v3',
        action='store_true',
        help='Use plain DER format instead of ServeV3'
    )
    
    args = parser.parse_args()

    try:
        # Determine ServeV3 usage
        use_serve_v3 = args.use_serve_v3 and not args.no_serve_v3

        # Create generator
        generator = MasterKeyGenerator(
            key_size=args.key_size,
            bit_size=args.bit_size
        )

        # Generate key pair
        generator.generate_rsa_keypair()

        # Save all files
        generator.save_files(
            output_dir=args.output_dir,
            config_version=args.config_version,
            use_serve_v3=use_serve_v3
        )
        
    except Exception as e:
        print(f"Error: {e}")
        return 1
    
    return 0


if __name__ == "__main__":
    exit(main())
