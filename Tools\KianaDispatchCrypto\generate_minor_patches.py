#!/usr/bin/env python3
"""
小版本补丁配置生成工具
====================

用于生成小版本增量更新的配置文件。
支持链式补丁和累积补丁两种策略。
"""

import json
import hashlib
import argparse
from typing import Dict, List, Optional
from datetime import datetime


class MinorPatchGenerator:
    """小版本补丁配置生成器"""
    
    def __init__(self, base_url: str = "https://autopatchcn.bh3.com"):
        self.base_url = base_url.rstrip('/')
        self.config = {
            "retcode": 0,
            "message": "OK",
            "data": {"game_packages": []}
        }
    
    def generate_md5(self, content: str) -> str:
        """生成示例MD5"""
        return hashlib.md5(content.encode()).hexdigest()
    
    def create_patch_package(self, 
                           from_version: str,
                           to_version: str,
                           patch_type: str,
                           base_size: int,
                           languages: List[str] = None) -> Dict:
        """创建补丁包配置"""
        
        if languages is None:
            languages = ["Chinese", "Japanese"]
        
        # 根据补丁类型调整大小
        size_multipliers = {
            "hotfix": 1.0,      # 基础大小
            "update": 2.0,      # 2倍大小
            "cumulative": 3.0,  # 3倍大小
            "major": 8.0        # 8倍大小
        }
        
        multiplier = size_multipliers.get(patch_type, 1.0)
        game_size = int(base_size * multiplier)
        audio_size = int(base_size * multiplier * 0.5)  # 语音包通常是游戏包的一半
        
        # 构建文件名
        game_filename = f"BH3_{from_version}_to_{to_version}_{patch_type}.zip"
        
        # 游戏主包
        game_pkg = {
            "url": f"{self.base_url}/ptpublic/rel/patches/{game_filename}",
            "md5": self.generate_md5(f"{from_version}_{to_version}_{patch_type}_game"),
            "size": str(game_size),
            "decompressed_size": str(game_size * 2),
            "path": f"{self.base_url}/ptpublic/rel/patches/{from_version}_to_{to_version}/",
            "pkg_version_file_name": f"pkg_version_{patch_type}"
        }
        
        # 语音包
        audio_pkgs = []
        for lang in languages:
            audio_filename = f"Audio_{lang}_{from_version}_to_{to_version}_{patch_type}.zip"
            audio_pkg = {
                "language": lang,
                "url": f"{self.base_url}/ptpublic/rel/patches/{audio_filename}",
                "md5": self.generate_md5(f"{from_version}_{to_version}_{patch_type}_{lang}"),
                "size": str(audio_size),
                "decompressed_size": str(audio_size * 2)
            }
            audio_pkgs.append(audio_pkg)
        
        return {
            "version": from_version,
            "game_pkgs": [game_pkg],
            "audio_pkgs": audio_pkgs,
            "res_list_url": f"{self.base_url}/ptpublic/rel/patches/{from_version}_to_{to_version}/pkg_version_{patch_type}"
        }
    
    def create_major_package(self,
                           version: str,
                           full_size: int,
                           languages: List[str] = None) -> Dict:
        """创建主版本包配置"""
        
        if languages is None:
            languages = ["Chinese", "Japanese"]
        
        # 主游戏包
        game_pkg = {
            "url": f"{self.base_url}/ptpublic/rel/{datetime.now().strftime('%Y%m%d')}_{version}_full/BH3_v{version}_full.zip",
            "md5": self.generate_md5(f"{version}_full_game"),
            "size": str(full_size),
            "decompressed_size": str(int(full_size * 1.33)),
            "path": f"{self.base_url}/ptpublic/rel/{datetime.now().strftime('%Y%m%d')}_{version}_full/",
            "pkg_version_file_name": "pkg_version"
        }
        
        # 语音包
        audio_pkgs = []
        for lang in languages:
            audio_size = int(full_size * 0.15)  # 语音包约为游戏包的15%
            audio_pkg = {
                "language": lang,
                "url": f"{self.base_url}/ptpublic/rel/{datetime.now().strftime('%Y%m%d')}_{version}_full/Audio_{lang}_{version}.zip",
                "md5": self.generate_md5(f"{version}_full_{lang}"),
                "size": str(audio_size),
                "decompressed_size": str(int(audio_size * 1.5))
            }
            audio_pkgs.append(audio_pkg)
        
        return {
            "version": version,
            "game_pkgs": [game_pkg],
            "audio_pkgs": audio_pkgs,
            "res_list_url": f"{self.base_url}/ptpublic/rel/{datetime.now().strftime('%Y%m%d')}_{version}_full/pkg_version"
        }
    
    def generate_minor_patches_config(self,
                                    major_version: str,
                                    minor_versions: List[str],
                                    strategy: str = "cumulative",
                                    base_patch_size: int = 134217728,  # 128MB
                                    full_game_size: int = 15728640000,  # ~14.6GB
                                    languages: List[str] = None) -> Dict:
        """
        生成小版本补丁配置
        
        Args:
            major_version: 主版本号 (如 "8.3")
            minor_versions: 小版本列表 (如 ["0", "1", "2", "3"])
            strategy: 补丁策略 ("cumulative" 或 "chain")
            base_patch_size: 基础补丁大小
            full_game_size: 完整游戏大小
            languages: 支持的语言
        """
        
        if languages is None:
            languages = ["Chinese", "Japanese"]
        
        # 确定最新版本
        latest_version = f"{major_version}.{max(minor_versions, key=int)}"
        
        # 创建主版本包
        major_pkg = self.create_major_package(latest_version, full_game_size, languages)
        
        # 创建补丁列表
        patches = []
        
        # 按版本倒序排列（最新的在前）
        sorted_versions = sorted(minor_versions, key=int, reverse=True)
        
        for i, minor_ver in enumerate(sorted_versions[1:], 1):  # 跳过最新版本
            from_version = f"{major_version}.{minor_ver}"
            
            # 确定补丁类型和大小
            version_gap = int(sorted_versions[0]) - int(minor_ver)
            
            if version_gap == 1:
                patch_type = "hotfix"
                size = base_patch_size
            elif version_gap == 2:
                patch_type = "update" 
                size = base_patch_size * 2
            else:
                patch_type = "cumulative"
                size = base_patch_size * 3
            
            # 根据策略创建补丁
            if strategy == "cumulative":
                # 累积补丁：每个版本都直接到最新版本
                patch = self.create_patch_package(
                    from_version, latest_version, patch_type, size, languages
                )
            else:  # chain
                # 链式补丁：每个版本到下一个版本
                to_version = f"{major_version}.{int(minor_ver) + 1}"
                patch = self.create_patch_package(
                    from_version, to_version, "hotfix", base_patch_size, languages
                )
            
            patches.append(patch)
        
        # 添加跨主版本的补丁（如 8.2.0 → 8.3.x）
        prev_major = f"{major_version.split('.')[0]}.{int(major_version.split('.')[1]) - 1}.0"
        major_patch = self.create_patch_package(
            prev_major, latest_version, "major", base_patch_size * 8, languages
        )
        patches.append(major_patch)
        
        # 构建完整配置
        game_package = {
            "game": {
                "biz": "bh3_global",
                "id": "bxPTXSET5t"
            },
            "main": {
                "major": major_pkg,
                "patches": patches
            }
        }
        
        self.config["data"]["game_packages"] = [game_package]
        
        # 添加元数据
        self.config["_metadata"] = {
            "生成时间": datetime.now().isoformat(),
            "策略": strategy,
            "主版本": major_version,
            "支持版本": [f"{major_version}.{v}" for v in minor_versions],
            "最新版本": latest_version,
            "补丁数量": len(patches),
            "语言支持": languages
        }
        
        return self.config
    
    def save_config(self, filename: str, config: Dict = None):
        """保存配置到文件"""
        if config is None:
            config = self.config
            
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(config, f, indent=2, ensure_ascii=False)
        
        print(f"✅ 配置已保存到: {filename}")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="小版本补丁配置生成工具",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  # 生成 8.3.x 系列的累积补丁配置
  python generate_minor_patches.py --major 8.3 --minors 0,1,2,3 --strategy cumulative
  
  # 生成 8.3.x 系列的链式补丁配置
  python generate_minor_patches.py --major 8.3 --minors 0,1,2,3 --strategy chain
  
  # 自定义基础URL和大小
  python generate_minor_patches.py --major 8.3 --minors 0,1,2,3 --base-url https://cdn.example.com --patch-size 268435456
        """
    )
    
    parser.add_argument('--major', required=True,
                       help='主版本号 (如: 8.3)')
    parser.add_argument('--minors', required=True,
                       help='小版本列表，逗号分隔 (如: 0,1,2,3)')
    parser.add_argument('--strategy', choices=['cumulative', 'chain'], default='cumulative',
                       help='补丁策略 (默认: cumulative)')
    parser.add_argument('--base-url', default='https://autopatchcn.bh3.com',
                       help='基础URL (默认: https://autopatchcn.bh3.com)')
    parser.add_argument('--patch-size', type=int, default=134217728,
                       help='基础补丁大小，字节 (默认: 134217728 = 128MB)')
    parser.add_argument('--full-size', type=int, default=15728640000,
                       help='完整游戏大小，字节 (默认: 15728640000 ≈ 14.6GB)')
    parser.add_argument('--languages', default='Chinese,Japanese',
                       help='支持的语言，逗号分隔 (默认: Chinese,Japanese)')
    parser.add_argument('-o', '--output',
                       help='输出文件名 (默认: 自动生成)')
    
    args = parser.parse_args()
    
    # 解析参数
    minor_versions = args.minors.split(',')
    languages = args.languages.split(',')
    
    # 生成输出文件名
    if args.output is None:
        latest_version = f"{args.major}.{max(minor_versions, key=int)}"
        args.output = f"patches_config_{latest_version}_{args.strategy}.json"
    
    # 创建生成器
    generator = MinorPatchGenerator(args.base_url)
    
    print(f"🔧 生成小版本补丁配置...")
    print(f"   主版本: {args.major}")
    print(f"   小版本: {', '.join(minor_versions)}")
    print(f"   策略: {args.strategy}")
    print(f"   语言: {', '.join(languages)}")
    print()
    
    # 生成配置
    config = generator.generate_minor_patches_config(
        major_version=args.major,
        minor_versions=minor_versions,
        strategy=args.strategy,
        base_patch_size=args.patch_size,
        full_game_size=args.full_size,
        languages=languages
    )
    
    # 保存配置
    generator.save_config(args.output, config)
    
    # 显示摘要
    patches = config["data"]["game_packages"][0]["main"]["patches"]
    print(f"\n📊 配置摘要:")
    print(f"   最新版本: {config['_metadata']['最新版本']}")
    print(f"   补丁数量: {len(patches)}")
    print(f"   支持版本: {', '.join(config['_metadata']['支持版本'])}")
    
    print(f"\n📋 补丁列表:")
    for patch in patches:
        from_ver = patch["version"]
        to_ver = config["_metadata"]["最新版本"]
        size_mb = int(patch["game_pkgs"][0]["size"]) // 1024 // 1024
        print(f"   • {from_ver} → {to_ver}: ~{size_mb}MB")
    
    print(f"\n📄 查看详细说明: 小版本补丁配置说明.md")
    
    return 0


if __name__ == "__main__":
    exit(main())
