#!/usr/bin/env python3
"""
Collapse Launcher 元数据连接测试工具
==================================

测试 Collapse Launcher 元数据服务器的连接性和数据完整性。
"""

import asyncio
import aiohttp
import json
import base64
import sys
from typing import Dict, List, Optional
import time


class MetadataConnectionTester:
    """元数据连接测试器"""
    
    def __init__(self):
        self.base_url = "https://api.github.com/repos/CollapseLauncher/CollapseLauncher-ReleaseRepo/contents/metadata"
        self.metadata_version = "v3"
        self.channels = ["stable", "preview"]
        self.timeout = aiohttp.ClientTimeout(total=30)
    
    async def test_connection(self) -> Dict:
        """测试基本连接"""
        
        print("🔗 测试基本连接...")
        
        results = {
            "github_api": False,
            "metadata_base": False,
            "channels": {},
            "errors": []
        }
        
        async with aiohttp.ClientSession(timeout=self.timeout) as session:
            # 测试 GitHub API
            try:
                async with session.get("https://api.github.com") as response:
                    if response.status == 200:
                        results["github_api"] = True
                        print("   ✓ GitHub API 连接正常")
                    else:
                        results["errors"].append(f"GitHub API 返回状态码: {response.status}")
                        print(f"   ❌ GitHub API 连接失败: {response.status}")
            except Exception as e:
                results["errors"].append(f"GitHub API 连接错误: {e}")
                print(f"   ❌ GitHub API 连接错误: {e}")
            
            # 测试元数据基础路径
            try:
                async with session.get(self.base_url) as response:
                    if response.status == 200:
                        results["metadata_base"] = True
                        print("   ✓ 元数据基础路径连接正常")
                    else:
                        results["errors"].append(f"元数据基础路径返回状态码: {response.status}")
                        print(f"   ❌ 元数据基础路径连接失败: {response.status}")
            except Exception as e:
                results["errors"].append(f"元数据基础路径连接错误: {e}")
                print(f"   ❌ 元数据基础路径连接错误: {e}")
            
            # 测试各个频道
            for channel in self.channels:
                print(f"   测试 {channel} 频道...")
                channel_url = f"{self.base_url}/{self.metadata_version}/{channel}"
                
                try:
                    async with session.get(channel_url) as response:
                        if response.status == 200:
                            results["channels"][channel] = True
                            print(f"     ✓ {channel} 频道连接正常")
                        else:
                            results["channels"][channel] = False
                            results["errors"].append(f"{channel} 频道返回状态码: {response.status}")
                            print(f"     ❌ {channel} 频道连接失败: {response.status}")
                except Exception as e:
                    results["channels"][channel] = False
                    results["errors"].append(f"{channel} 频道连接错误: {e}")
                    print(f"     ❌ {channel} 频道连接错误: {e}")
        
        return results
    
    async def test_stamp_file(self, channel: str = "stable") -> Dict:
        """测试 stamp.json 文件"""
        
        print(f"📋 测试 {channel} 频道的 stamp.json...")
        
        results = {
            "accessible": False,
            "valid_json": False,
            "has_bh3_sea": False,
            "stamp_data": None,
            "errors": []
        }
        
        stamp_url = f"{self.base_url}/{self.metadata_version}/{channel}/stamp.json"
        
        async with aiohttp.ClientSession(timeout=self.timeout) as session:
            try:
                async with session.get(stamp_url) as response:
                    if response.status == 200:
                        results["accessible"] = True
                        print("   ✓ stamp.json 文件可访问")
                        
                        # 获取文件内容
                        content = await response.json()
                        
                        # GitHub API 返回的是 base64 编码的内容
                        if 'content' in content:
                            decoded_content = base64.b64decode(content['content']).decode('utf-8')
                            
                            try:
                                stamp_data = json.loads(decoded_content)
                                results["valid_json"] = True
                                results["stamp_data"] = stamp_data
                                print("   ✓ stamp.json 格式有效")
                                
                                # 检查是否包含崩坏3东南亚服配置
                                for key, stamp in stamp_data.items():
                                    if isinstance(stamp, dict):
                                        game_name = stamp.get('GameName', '')
                                        game_region = stamp.get('GameRegion', '')
                                        
                                        if 'Honkai Impact 3rd' in game_name and 'Southeast Asia' in game_region:
                                            results["has_bh3_sea"] = True
                                            print(f"   ✓ 找到崩坏3东南亚服配置: {key}")
                                            print(f"     MetadataPath: {stamp.get('MetadataPath', 'N/A')}")
                                            print(f"     LastUpdated: {stamp.get('LastUpdated', 'N/A')}")
                                            break
                                
                                if not results["has_bh3_sea"]:
                                    print("   ⚠️  未找到崩坏3东南亚服配置")
                                    
                            except json.JSONDecodeError as e:
                                results["errors"].append(f"stamp.json JSON 解析错误: {e}")
                                print(f"   ❌ stamp.json JSON 解析错误: {e}")
                        else:
                            results["errors"].append("GitHub API 响应中缺少 content 字段")
                            print("   ❌ GitHub API 响应格式异常")
                    else:
                        results["errors"].append(f"stamp.json 返回状态码: {response.status}")
                        print(f"   ❌ stamp.json 访问失败: {response.status}")
                        
            except Exception as e:
                results["errors"].append(f"stamp.json 访问错误: {e}")
                print(f"   ❌ stamp.json 访问错误: {e}")
        
        return results
    
    async def test_bh3_sea_config(self, channel: str = "stable") -> Dict:
        """测试崩坏3东南亚服配置文件"""
        
        print(f"🎮 测试崩坏3东南亚服配置文件...")
        
        results = {
            "config_found": False,
            "config_accessible": False,
            "config_valid": False,
            "serve_v3_data": False,
            "config_path": None,
            "errors": []
        }
        
        # 首先获取 stamp 信息
        stamp_results = await self.test_stamp_file(channel)
        
        if not stamp_results["has_bh3_sea"] or not stamp_results["stamp_data"]:
            results["errors"].append("无法从 stamp.json 获取崩坏3东南亚服配置路径")
            print("   ❌ 无法获取配置路径")
            return results
        
        # 查找崩坏3东南亚服的配置路径
        config_path = None
        for key, stamp in stamp_results["stamp_data"].items():
            if isinstance(stamp, dict):
                game_name = stamp.get('GameName', '')
                game_region = stamp.get('GameRegion', '')
                
                if 'Honkai Impact 3rd' in game_name and 'Southeast Asia' in game_region:
                    config_path = stamp.get('MetadataPath')
                    break
        
        if not config_path:
            results["errors"].append("stamp.json 中未找到崩坏3东南亚服的 MetadataPath")
            print("   ❌ 未找到配置路径")
            return results
        
        results["config_found"] = True
        results["config_path"] = config_path
        print(f"   ✓ 找到配置路径: {config_path}")
        
        # 测试配置文件访问
        config_url = f"{self.base_url}/{self.metadata_version}/{channel}/{config_path}"
        
        async with aiohttp.ClientSession(timeout=self.timeout) as session:
            try:
                async with session.get(config_url) as response:
                    if response.status == 200:
                        results["config_accessible"] = True
                        print("   ✓ 配置文件可访问")
                        
                        # 获取文件内容
                        content = await response.json()
                        
                        if 'content' in content:
                            decoded_content = base64.b64decode(content['content']).decode('utf-8')
                            
                            try:
                                config_data = json.loads(decoded_content)
                                results["config_valid"] = True
                                print("   ✓ 配置文件 JSON 格式有效")
                                
                                # 检查是否包含 ServeV3 数据
                                serve_v3_fields = 0
                                for key, value in config_data.items():
                                    if isinstance(value, str) and self._is_likely_serve_v3(value):
                                        serve_v3_fields += 1
                                
                                if serve_v3_fields > 0:
                                    results["serve_v3_data"] = True
                                    print(f"   ✓ 检测到 {serve_v3_fields} 个 ServeV3 加密字段")
                                else:
                                    print("   ○ 未检测到 ServeV3 加密字段")
                                    
                            except json.JSONDecodeError as e:
                                results["errors"].append(f"配置文件 JSON 解析错误: {e}")
                                print(f"   ❌ 配置文件 JSON 解析错误: {e}")
                        else:
                            results["errors"].append("GitHub API 响应中缺少 content 字段")
                            print("   ❌ GitHub API 响应格式异常")
                    else:
                        results["errors"].append(f"配置文件返回状态码: {response.status}")
                        print(f"   ❌ 配置文件访问失败: {response.status}")
                        
            except Exception as e:
                results["errors"].append(f"配置文件访问错误: {e}")
                print(f"   ❌ 配置文件访问错误: {e}")
        
        return results
    
    def _is_likely_serve_v3(self, data: str) -> bool:
        """检查字符串是否可能是 ServeV3 数据"""
        
        if len(data) < 40:  # ServeV3 数据通常比较长
            return False
        
        try:
            # 尝试 base64 解码
            decoded = base64.b64decode(data, validate=True)
            
            # 检查 Collapse 签名
            if len(decoded) >= 8:
                signature = int.from_bytes(decoded[:8], byteorder='little')
                return signature == 7310310183885631299
                
        except Exception:
            pass
        
        return False
    
    async def run_full_test(self) -> Dict:
        """运行完整测试"""
        
        print("🧪 Collapse Launcher 元数据连接测试")
        print("=" * 50)
        
        full_results = {
            "connection": None,
            "stamp": {},
            "config": {},
            "summary": {
                "total_errors": 0,
                "critical_errors": [],
                "warnings": []
            }
        }
        
        # 测试基本连接
        full_results["connection"] = await self.test_connection()
        
        # 测试各个频道
        for channel in self.channels:
            print(f"\n📋 测试 {channel} 频道")
            print("-" * 30)
            
            # 测试 stamp 文件
            stamp_results = await self.test_stamp_file(channel)
            full_results["stamp"][channel] = stamp_results
            
            # 测试配置文件
            config_results = await self.test_bh3_sea_config(channel)
            full_results["config"][channel] = config_results
        
        # 生成摘要
        self._generate_summary(full_results)
        
        return full_results
    
    def _generate_summary(self, results: Dict):
        """生成测试摘要"""
        
        summary = results["summary"]
        
        # 收集所有错误
        all_errors = []
        
        if results["connection"]["errors"]:
            all_errors.extend(results["connection"]["errors"])
        
        for channel in self.channels:
            if channel in results["stamp"]:
                all_errors.extend(results["stamp"][channel]["errors"])
            if channel in results["config"]:
                all_errors.extend(results["config"][channel]["errors"])
        
        summary["total_errors"] = len(all_errors)
        
        # 分类错误
        for error in all_errors:
            if any(keyword in error.lower() for keyword in ["连接", "timeout", "network"]):
                summary["critical_errors"].append(error)
            else:
                summary["warnings"].append(error)
        
        # 打印摘要
        print(f"\n📊 测试摘要")
        print("=" * 50)
        
        if summary["total_errors"] == 0:
            print("✅ 所有测试通过，元数据服务正常")
        else:
            print(f"⚠️  发现 {summary['total_errors']} 个问题")
            
            if summary["critical_errors"]:
                print(f"\n❌ 严重错误 ({len(summary['critical_errors'])}):")
                for error in summary["critical_errors"]:
                    print(f"   • {error}")
            
            if summary["warnings"]:
                print(f"\n⚠️  警告 ({len(summary['warnings'])}):")
                for warning in summary["warnings"]:
                    print(f"   • {warning}")
        
        # 建议
        print(f"\n💡 建议:")
        if not results["connection"]["github_api"]:
            print("   • 检查网络连接和防火墙设置")
            print("   • 尝试使用 VPN 或代理")
        
        if summary["critical_errors"]:
            print("   • 清除 Collapse Launcher 元数据缓存")
            print("   • 重启网络连接")
            print("   • 稍后重试")
        
        if summary["total_errors"] == 0:
            print("   • 如果 Collapse Launcher 仍有问题，请清除缓存后重试")


async def main():
    """主函数"""
    
    tester = MetadataConnectionTester()
    
    try:
        results = await tester.run_full_test()
        return 0 if results["summary"]["total_errors"] == 0 else 1
        
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {e}")
        return 1


if __name__ == "__main__":
    exit(asyncio.run(main()))
