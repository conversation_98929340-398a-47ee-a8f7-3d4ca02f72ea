#!/usr/bin/env python3
"""
Test script to verify ServeV3 format master key generation and usage.
"""

import json
import tempfile
import base64
from pathlib import Path

from generate_master_key import Master<PERSON>eyGenerator
import sys
import os

# Add ConfigCrypto to path
sys.path.append(str(Path(__file__).parent.parent / "ConfigCrypto"))
from crypto_tool import CollapseDecryptor


def test_serve_v3_master_key_generation():
    """Test generation of ServeV3 format master key."""
    
    print("=" * 60)
    print("Testing ServeV3 Master Key Generation")
    print("=" * 60)
    
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)
        
        # Test 1: Generate ServeV3 format master key
        print("Test 1: Generating ServeV3 format master key...")
        generator_v3 = MasterKeyGenerator(key_size=1024, bit_size=128)
        generator_v3.generate_rsa_keypair()
        
        master_config_v3 = generator_v3.create_master_key_config(use_serve_v3=True)
        
        # Verify ServeV3 format
        key_data_v3 = master_config_v3['Key']
        decoded_v3 = base64.b64decode(key_data_v3)
        
        if len(decoded_v3) >= 8:
            signature = int.from_bytes(decoded_v3[:8], byteorder='little')
            collapse_signature = 7310310183885631299
            
            if signature == collapse_signature:
                print("✓ ServeV3 master key generated successfully")
                print(f"  Signature: {signature}")
                print(f"  Key length: {len(decoded_v3)} bytes")
            else:
                print(f"❌ Wrong signature: {signature}")
                return False
        else:
            print(f"❌ Key data too short: {len(decoded_v3)} bytes")
            return False
        
        # Test 2: Generate plain DER format master key
        print("\nTest 2: Generating plain DER format master key...")
        generator_der = MasterKeyGenerator(key_size=1024, bit_size=128)
        generator_der.generate_rsa_keypair()
        
        master_config_der = generator_der.create_master_key_config(use_serve_v3=False)
        
        # Verify DER format
        key_data_der = master_config_der['Key']
        decoded_der = base64.b64decode(key_data_der)
        
        # DER format should start with specific bytes for PKCS8
        if decoded_der.startswith(b'\x30\x82'):  # ASN.1 SEQUENCE
            print("✓ Plain DER master key generated successfully")
            print(f"  Key length: {len(decoded_der)} bytes")
        else:
            print(f"❌ Invalid DER format")
            return False
        
        return True


def test_master_key_loading():
    """Test loading of both ServeV3 and DER format master keys."""
    
    print("\n" + "=" * 60)
    print("Testing Master Key Loading")
    print("=" * 60)
    
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)
        
        # Generate both formats
        generator = MasterKeyGenerator(key_size=1024, bit_size=128)
        generator.generate_rsa_keypair()
        
        # Save ServeV3 format
        master_config_v3 = generator.create_master_key_config(use_serve_v3=True)
        v3_key_path = temp_path / "master_key_v3.json"
        with open(v3_key_path, 'w') as f:
            json.dump(master_config_v3, f, indent=2)
        
        # Save DER format
        master_config_der = generator.create_master_key_config(use_serve_v3=False)
        der_key_path = temp_path / "master_key_der.json"
        with open(der_key_path, 'w') as f:
            json.dump(master_config_der, f, indent=2)
        
        # Test loading ServeV3 format
        print("Test 1: Loading ServeV3 format master key...")
        try:
            decryptor_v3 = CollapseDecryptor(str(v3_key_path))
            print("✓ ServeV3 master key loaded successfully")
            print(f"  Key size: {decryptor_v3.private_key.key_size} bits")
        except Exception as e:
            print(f"❌ Failed to load ServeV3 master key: {e}")
            return False
        
        # Test loading DER format
        print("\nTest 2: Loading DER format master key...")
        try:
            decryptor_der = CollapseDecryptor(str(der_key_path))
            print("✓ DER master key loaded successfully")
            print(f"  Key size: {decryptor_der.private_key.key_size} bits")
        except Exception as e:
            print(f"❌ Failed to load DER master key: {e}")
            return False
        
        # Verify both keys work for encryption/decryption
        print("\nTest 3: Verifying encryption/decryption with both keys...")
        test_data = "https://example.com/test/api"
        
        # Test ServeV3 key
        try:
            encrypted_v3 = decryptor_v3.encrypt_field(test_data)
            decrypted_v3 = decryptor_v3.decrypt_field(encrypted_v3)
            
            if decrypted_v3 == test_data:
                print("✓ ServeV3 key encryption/decryption works")
            else:
                print(f"❌ ServeV3 key data mismatch")
                return False
        except Exception as e:
            print(f"❌ ServeV3 key encryption/decryption failed: {e}")
            return False
        
        # Test DER key
        try:
            encrypted_der = decryptor_der.encrypt_field(test_data)
            decrypted_der = decryptor_der.decrypt_field(encrypted_der)
            
            if decrypted_der == test_data:
                print("✓ DER key encryption/decryption works")
            else:
                print(f"❌ DER key data mismatch")
                return False
        except Exception as e:
            print(f"❌ DER key encryption/decryption failed: {e}")
            return False
        
        return True


def test_serve_v3_header_analysis():
    """Test analysis of ServeV3 header in master key."""
    
    print("\n" + "=" * 60)
    print("Testing ServeV3 Header Analysis")
    print("=" * 60)
    
    generator = MasterKeyGenerator(key_size=1024, bit_size=128)
    generator.generate_rsa_keypair()
    
    master_config = generator.create_master_key_config(use_serve_v3=True)
    key_data = master_config['Key']
    decoded_data = base64.b64decode(key_data)
    
    if len(decoded_data) >= 32:
        # Parse ServeV3 header
        signature = int.from_bytes(decoded_data[0:8], byteorder='little')
        attributes = int.from_bytes(decoded_data[8:16], byteorder='little')
        compressed_size = int.from_bytes(decoded_data[16:24], byteorder='little')
        decompressed_size = int.from_bytes(decoded_data[24:32], byteorder='little')
        
        compression_type = attributes & 0xFF
        is_encrypted = (attributes >> 8) & 0xFF
        
        print(f"ServeV3 Master Key Header Analysis:")
        print(f"  Signature: {signature} (✓ correct)")
        print(f"  Compression Type: {compression_type} (0=None)")
        print(f"  Is Encrypted: {is_encrypted} (0=No, master key not encrypted)")
        print(f"  Compressed Size: {compressed_size} bytes")
        print(f"  Decompressed Size: {decompressed_size} bytes")
        print(f"  Header Size: 32 bytes")
        print(f"  Total Size: {len(decoded_data)} bytes")
        
        # Verify master key is not encrypted
        if is_encrypted == 0:
            print("✓ Master key correctly marked as not encrypted")
            return True
        else:
            print("❌ Master key incorrectly marked as encrypted")
            return False
    else:
        print(f"❌ ServeV3 header too short: {len(decoded_data)} bytes")
        return False


def test_command_line_generation():
    """Test command line generation of ServeV3 master keys."""
    
    print("\n" + "=" * 60)
    print("Testing Command Line Generation")
    print("=" * 60)
    
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)
        
        # Test ServeV3 generation
        print("Test 1: Command line ServeV3 generation...")
        v3_output = temp_path / "v3_output"
        
        generator_v3 = MasterKeyGenerator(key_size=1024, bit_size=128)
        generator_v3.generate_rsa_keypair()
        generator_v3.save_files(str(v3_output), use_serve_v3=True)
        
        # Verify files exist
        config_file = v3_output / "config_master.json"
        if config_file.exists():
            with open(config_file, 'r') as f:
                config = json.load(f)
            
            # Check if it's ServeV3 format
            key_data = config['Key']
            decoded = base64.b64decode(key_data)
            signature = int.from_bytes(decoded[:8], byteorder='little')
            
            if signature == 7310310183885631299:
                print("✓ Command line ServeV3 generation successful")
            else:
                print("❌ Command line ServeV3 generation failed")
                return False
        else:
            print("❌ Config file not generated")
            return False
        
        # Test DER generation
        print("\nTest 2: Command line DER generation...")
        der_output = temp_path / "der_output"
        
        generator_der = MasterKeyGenerator(key_size=1024, bit_size=128)
        generator_der.generate_rsa_keypair()
        generator_der.save_files(str(der_output), use_serve_v3=False)
        
        # Verify files exist
        config_file = der_output / "config_master.json"
        if config_file.exists():
            with open(config_file, 'r') as f:
                config = json.load(f)
            
            # Check if it's DER format
            key_data = config['Key']
            decoded = base64.b64decode(key_data)
            
            if decoded.startswith(b'\x30\x82'):  # ASN.1 SEQUENCE
                print("✓ Command line DER generation successful")
            else:
                print("❌ Command line DER generation failed")
                return False
        else:
            print("❌ Config file not generated")
            return False
        
        return True


def main():
    """Run all tests."""
    print("Testing ServeV3 Master Key Support")
    print("=" * 60)
    
    success_count = 0
    total_tests = 4
    
    # Test 1: ServeV3 master key generation
    if test_serve_v3_master_key_generation():
        success_count += 1
    
    # Test 2: Master key loading
    if test_master_key_loading():
        success_count += 1
    
    # Test 3: ServeV3 header analysis
    if test_serve_v3_header_analysis():
        success_count += 1
    
    # Test 4: Command line generation
    if test_command_line_generation():
        success_count += 1
    
    # Summary
    print("\n" + "=" * 60)
    print("Test Summary")
    print("=" * 60)
    print(f"Passed: {success_count}/{total_tests}")
    
    if success_count == total_tests:
        print("🎉 All tests passed! ServeV3 master key support is working correctly.")
        print("\nUsage examples:")
        print("  # Generate ServeV3 format master key (default)")
        print("  python generate_master_key.py --key-size 1024 --output-dir ./output")
        print("  # Generate plain DER format master key")
        print("  python generate_master_key.py --key-size 1024 --no-serve-v3 --output-dir ./output")
        return True
    else:
        print("❌ Some tests failed. Please check the implementation.")
        return False


if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
