#!/usr/bin/env python3
"""
修复后的东南亚服测试脚本
=======================

修复了 token 和服务器名称的问题。
"""

import asyncio
import json
from game_resource_fetcher import GameResourceFetcher


async def test_sea_with_correct_config():
    """使用正确配置测试东南亚服"""
    
    print("🌏 崩坏3东南亚服修复测试")
    print("=" * 50)
    
    # 正确的东南亚服配置
    sea_config = {
        "dispatcher_key": "bgyzmmhy",
        "version": "8.3",
        "dispatch_url": "https://osglobal1.honkaiimpact3.com/query_dispatch",
        "channel": "os_pc",
        "token": "82cb3ccb0be3ee59d8b51e2139c18914"  # 正确的东南亚服 token
    }
    
    print("📋 正确的东南亚服配置:")
    print(f"   调度密钥: {sea_config['dispatcher_key']}")
    print(f"   游戏版本: {sea_config['version']}")
    print(f"   分发服务器: {sea_config['dispatch_url']}")
    print(f"   渠道名称: {sea_config['channel']}")
    print(f"   认证令牌: {sea_config['token'][:20]}... (东南亚服专用)")
    print()
    
    try:
        async with GameResourceFetcher(sea_config["dispatcher_key"], sea_config["version"]) as fetcher:
            
            print("🔄 步骤 1: 获取分发服务器响应")
            print("-" * 40)
            
            # 使用修复后的方法，会自动检测并使用正确的 token
            dispatch_data = await fetcher.fetch_dispatch_response(
                sea_config["dispatch_url"],
                sea_config["channel"]  # 不传 token，让系统自动检测
            )
            
            print("✅ 分发服务器响应成功!")
            print(f"   返回码: {dispatch_data.get('retcode')}")
            
            # 显示可用服务器
            region_list = dispatch_data.get("region_list", [])
            print(f"   可用服务器数量: {len(region_list)}")
            
            if region_list:
                print("\n   🌐 可用服务器列表:")
                for region in region_list:
                    name = region.get("name", "unknown")
                    title = region.get("title", "无标题")
                    url = region.get("dispatch_url", "")
                    print(f"      • {name}: {title}")
                    print(f"        URL: {url}")
                print()
                
                print("🔄 步骤 2: 自动选择合适的游戏服务器")
                print("-" * 40)
                
                # 使用修复后的方法，会自动选择合适的服务器
                gameserver_data = await fetcher.fetch_gameserver_info(
                    dispatch_data,
                    None,  # 让系统自动选择服务器
                    sea_config["channel"]  # 不传 token，让系统自动检测
                )
                
                print("✅ 游戏服务器响应成功!")
                print(f"   返回码: {gameserver_data.get('retcode')}")
                print(f"   数据就绪: {gameserver_data.get('is_data_ready', 'unknown')}")
                
                # 提取资源 URL
                asset_bundle_urls, external_resource_urls = fetcher.extract_resource_urls(gameserver_data)
                
                if asset_bundle_urls:
                    print(f"\n   📦 资源包 URL:")
                    for i, url in enumerate(asset_bundle_urls, 1):
                        print(f"      {i}. {url}")
                    
                    print(f"\n   🔗 外部资源 URL:")
                    for i, url in enumerate(external_resource_urls, 1):
                        print(f"      {i}. {url}")
                    
                    # 构建下载 URL
                    base_url = asset_bundle_urls[0]
                    pkg_version_url = fetcher.build_pkg_version_url(base_url)
                    cache_urls = fetcher.build_cache_urls(base_url)
                    
                    print(f"\n🔄 步骤 3: 构建下载 URL")
                    print("-" * 40)
                    print("✅ 下载 URL 构建完成!")
                    
                    print(f"\n   📄 pkg_version 文件:")
                    print(f"      {pkg_version_url}")
                    
                    print(f"\n   🗂️ 缓存资源文件:")
                    for cache_type, url in cache_urls.items():
                        print(f"      {cache_type}: {url}")
                    
                    print(f"\n🔄 步骤 4: 尝试获取 pkg_version 文件")
                    print("-" * 40)
                    
                    pkg_content = await fetcher.fetch_pkg_version(pkg_version_url)
                    
                    if pkg_content:
                        print("✅ pkg_version 文件获取成功!")
                        print(f"   文件大小: {len(pkg_content)} 字节")
                        
                        lines = pkg_content.strip().split('\n')
                        print(f"   总行数: {len(lines)}")
                        
                        if lines:
                            print(f"\n   📝 内容预览 (前 3 行):")
                            for i, line in enumerate(lines[:3]):
                                print(f"      {i+1}: {line[:80]}{'...' if len(line) > 80 else ''}")
                    else:
                        print("⚠️  pkg_version 文件不可用 (可能使用 Sophon 系统)")
                    
                    # 保存结果
                    result = {
                        "测试状态": "成功",
                        "服务器区域": "东南亚 (Southeast Asia)",
                        "可用服务器": [{"name": r.get("name"), "title": r.get("title")} for r in region_list],
                        "资源信息": {
                            "基础URL": base_url,
                            "pkg_version": pkg_version_url,
                            "缓存资源": cache_urls
                        },
                        "配置信息": sea_config
                    }
                    
                    with open("东南亚服测试结果.json", "w", encoding="utf-8") as f:
                        json.dump(result, f, indent=2, ensure_ascii=False)
                    
                    print(f"\n💾 测试结果已保存到: 东南亚服测试结果.json")
                    
                else:
                    print("❌ 未找到资源包 URL")
            else:
                print("❌ 未找到可用服务器")
                
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return 1
    
    print(f"\n🎉 东南亚服测试完成!")
    print("=" * 50)
    print()
    print("📝 修复要点:")
    print("• 使用正确的东南亚服 token: 82cb3ccb0be3ee59d8b51e2139c18914")
    print("• 自动检测服务器名称 (不再硬编码 overseas01)")
    print("• 添加 lang=EN 参数支持多语言")
    print("• 智能选择可用的服务器")
    print()
    print("🚀 使用方法:")
    print("python game_resource_fetcher.py -k bgyzmmhy -v 8.3 \\")
    print("  -u 'https://osglobal1.honkaiimpact3.com/query_dispatch'")
    print("  # 现在会自动检测并使用正确的配置!")
    
    return 0


async def compare_cn_vs_sea():
    """对比国服和东南亚服的响应"""
    
    print("\n📊 国服 vs 东南亚服对比测试")
    print("=" * 50)
    
    configs = {
        "国服": {
            "dispatcher_key": "bgyzmmhy",
            "version": "8.3",
            "dispatch_url": "https://global1.bh3.com/query_dispatch",
            "channel": "gf_pc",
            "token": "a9366694c30364d479dd142f9cf0de54"
        },
        "东南亚服": {
            "dispatcher_key": "bgyzmmhy", 
            "version": "8.3",
            "dispatch_url": "https://osglobal1.honkaiimpact3.com/query_dispatch",
            "channel": "os_pc",
            "token": "82cb3ccb0be3ee59d8b51e2139c18914"
        }
    }
    
    results = {}
    
    for region_name, config in configs.items():
        print(f"\n🔄 测试 {region_name}...")
        try:
            async with GameResourceFetcher(config["dispatcher_key"], config["version"]) as fetcher:
                dispatch_data = await fetcher.fetch_dispatch_response(
                    config["dispatch_url"],
                    config["channel"]
                )
                
                servers = [r.get("name") for r in dispatch_data.get("region_list", [])]
                results[region_name] = {
                    "状态": "成功",
                    "返回码": dispatch_data.get("retcode"),
                    "服务器数量": len(servers),
                    "可用服务器": servers
                }
                print(f"   ✅ {region_name} 测试成功")
                print(f"   📡 可用服务器: {', '.join(servers)}")
                
        except Exception as e:
            results[region_name] = {
                "状态": "失败",
                "错误": str(e)
            }
            print(f"   ❌ {region_name} 测试失败: {e}")
    
    print(f"\n📋 对比结果:")
    for region_name, result in results.items():
        print(f"\n{region_name}:")
        for key, value in result.items():
            print(f"   {key}: {value}")


if __name__ == "__main__":
    try:
        print("🛠️  崩坏3东南亚服修复测试工具")
        print("=" * 60)
        
        choice = input("\n选择测试模式:\n1. 东南亚服完整测试\n2. 国服 vs 东南亚服对比\n请选择 (1/2): ").strip()
        
        if choice == "1":
            result = asyncio.run(test_sea_with_correct_config())
        elif choice == "2":
            result = asyncio.run(compare_cn_vs_sea())
        else:
            print("无效选择")
            result = 1
            
        exit(result)
        
    except KeyboardInterrupt:
        print("\n\n用户取消操作")
        exit(0)
    except Exception as e:
        print(f"\n错误: {e}")
        exit(1)
