#!/usr/bin/env python3
"""
演示完整流程 - 从 Dispatch 响应到游戏资源获取
===========================================

这个脚本演示了从解密 dispatch 响应到获取游戏安装包和更新补丁的完整流程。

使用您提供的实际数据进行演示。
"""

import asyncio
import json
from game_resource_fetcher import GameResourceFetcher


async def demo_complete_flow():
    """演示完整的游戏资源获取流程"""
    
    print("=" * 60)
    print("🎮 miHoYo 游戏资源获取完整流程演示")
    print("=" * 60)
    print()
    
    # 配置信息（来自您的配置文件）
    config = {
        "dispatcher_key": "bgyzmmhy",
        "version": "8.3",
        "dispatch_url": "https://global1.bh3.com/query_dispatch",
        "target_server": "pc01"
    }
    
    print("📋 配置信息:")
    print(f"   调度密钥: {config['dispatcher_key']}")
    print(f"   游戏版本: {config['version']}")
    print(f"   分发服务器: {config['dispatch_url']}")
    print(f"   目标服务器: {config['target_server']}")
    print()
    
    try:
        async with GameResourceFetcher(config["dispatcher_key"], config["version"]) as fetcher:
            
            print("🔄 步骤 1: 获取分发服务器响应")
            print("-" * 40)
            dispatch_data = await fetcher.fetch_dispatch_response(config["dispatch_url"])
            
            print("✅ 分发服务器响应解密成功!")
            print(f"   返回码: {dispatch_data.get('retcode')}")
            print(f"   可用服务器数量: {len(dispatch_data.get('region_list', []))}")
            
            # 显示可用服务器
            print("\n   📡 可用服务器列表:")
            for region in dispatch_data.get("region_list", []):
                name = region.get("name", "unknown")
                title = region.get("title", "无标题")
                print(f"      • {name}: {title}")
            print()
            
            print("🔄 步骤 2: 查询游戏服务器详细信息")
            print("-" * 40)
            gameserver_data = await fetcher.fetch_gameserver_info(dispatch_data, config["target_server"])
            
            print(f"✅ 游戏服务器 '{config['target_server']}' 信息获取成功!")
            print(f"   返回码: {gameserver_data.get('retcode')}")
            print(f"   数据就绪: {gameserver_data.get('is_data_ready', 'unknown')}")
            
            # 提取资源 URL
            asset_bundle_urls, external_resource_urls = fetcher.extract_resource_urls(gameserver_data)
            
            print(f"\n   📦 资源包 URL 数量: {len(asset_bundle_urls)}")
            print(f"   🔗 外部资源 URL 数量: {len(external_resource_urls)}")
            
            if asset_bundle_urls:
                print(f"\n   🎯 主要资源包 URL:")
                print(f"      {asset_bundle_urls[0]}")
            print()
            
            print("🔄 步骤 3: 构建下载 URL")
            print("-" * 40)
            
            if asset_bundle_urls:
                base_url = asset_bundle_urls[0]
                pkg_version_url = fetcher.build_pkg_version_url(base_url)
                cache_urls = fetcher.build_cache_urls(base_url)
                
                print("✅ 下载 URL 构建完成!")
                print(f"\n   📄 pkg_version 文件:")
                print(f"      {pkg_version_url}")
                
                print(f"\n   🗂️ 缓存资源文件:")
                for cache_type, url in cache_urls.items():
                    print(f"      {cache_type}: {url}")
                print()
                
                print("🔄 步骤 4: 尝试获取 pkg_version 文件")
                print("-" * 40)
                
                pkg_version_content = await fetcher.fetch_pkg_version(pkg_version_url)
                
                if pkg_version_content:
                    print("✅ pkg_version 文件获取成功!")
                    print(f"   文件大小: {len(pkg_version_content)} 字节")
                    
                    # 尝试解析前几行
                    lines = pkg_version_content.strip().split('\n')
                    print(f"   行数: {len(lines)}")
                    
                    if lines:
                        print(f"\n   📝 文件内容预览 (前 3 行):")
                        for i, line in enumerate(lines[:3]):
                            print(f"      {i+1}: {line[:80]}{'...' if len(line) > 80 else ''}")
                else:
                    print("⚠️  pkg_version 文件不可用 (可能使用 Sophon 系统)")
                print()
                
                print("🔄 步骤 5: 生成完整资源信息")
                print("-" * 40)
                
                # 生成完整的资源信息
                complete_info = {
                    "游戏信息": {
                        "版本": config["version"],
                        "服务器": config["target_server"],
                        "状态": "资源获取成功"
                    },
                    "下载地址": {
                        "资源包基础URL": base_url,
                        "pkg_version": pkg_version_url,
                        "缓存资源": cache_urls
                    },
                    "安装方式": {
                        "传统安装": [
                            "1. 下载 pkg_version 文件",
                            "2. 解析文件列表",
                            "3. 下载各个游戏文件",
                            "4. 解压并安装到游戏目录"
                        ],
                        "Sophon安装": [
                            "1. 使用配置中的 LauncherResourceChunksURL",
                            "2. 从 Sophon API 获取 manifest",
                            "3. 并行下载分块文件",
                            "4. 从分块重组完整文件"
                        ]
                    },
                    "Sophon配置": {
                        "MainUrl": "https://api-takumi.mihoyo.com/downloader/sophon_chunk/api/getBuild?branch=main&package_id=jdEaAE9pKa&password=h6XjD8ReKlR1",
                        "PreloadUrl": "https://api-takumi.mihoyo.com/downloader/sophon_chunk/api/getBuild?branch=predownload&package_id=jdEaAE9pKa&password=OAvMeb9icV0x",
                        "PatchUrl": "https://api-takumi.mihoyo.com/downloader/sophon_chunk/api/getPatchBuild?branch=main&password=h6XjD8ReKlR1&package_id=jdEaAE9pKa"
                    }
                }
                
                print("✅ 完整资源信息生成成功!")
                print("\n📊 资源信息摘要:")
                print(f"   • 基础URL: {base_url}")
                print(f"   • pkg_version: {'可用' if pkg_version_content else '不可用'}")
                print(f"   • 缓存资源类型: {len(cache_urls)} 种")
                print(f"   • 支持安装方式: 传统 + Sophon")
                print()
                
                # 保存完整信息到文件
                with open("游戏资源信息.json", "w", encoding="utf-8") as f:
                    json.dump(complete_info, f, indent=2, ensure_ascii=False)
                
                print("💾 完整资源信息已保存到: 游戏资源信息.json")
                print()
                
            else:
                print("❌ 未找到资源包 URL")
                return
            
            print("🎉 流程演示完成!")
            print("=" * 60)
            print()
            print("📝 总结:")
            print("1. ✅ 成功解密 dispatch 响应")
            print("2. ✅ 获取游戏服务器信息") 
            print("3. ✅ 提取资源下载地址")
            print("4. ✅ 构建安装包 URL")
            print(f"5. {'✅' if pkg_version_content else '⚠️ '} {'获取' if pkg_version_content else '检测'} pkg_version 文件")
            print("6. ✅ 生成完整资源信息")
            print()
            print("🚀 下一步操作:")
            print("• 使用生成的 URL 下载游戏文件")
            print("• 或使用 Sophon API 进行分块下载")
            print("• 参考 Collapse Launcher 的实现进行完整安装")
            
    except Exception as e:
        print(f"❌ 错误: {e}")
        return 1
    
    return 0


async def demo_with_existing_data():
    """使用您提供的解密数据进行演示"""
    
    print("\n" + "=" * 60)
    print("🔍 使用现有解密数据进行演示")
    print("=" * 60)
    
    # 您提供的解密结果
    existing_dispatch_data = {
        "region_list": [
            {
                "dispatch_url": "https://outer-dp-yyb01.bh3.com/query_gameserver",
                "ext": {"1794460900": "HIDE"},
                "name": "yyb01",
                "retcode": 0,
                "title": "应用宝服"
            },
            {
                "dispatch_url": "https://outer-dp-bb01.bh3.com/query_gameserver",
                "ext": {"1794460900": "HIDE"},
                "name": "bb01",
                "retcode": 0,
                "title": "BiliBili服"
            },
            {
                "dispatch_url": "https://outer-dp-pc01.bh3.com/query_gameserver",
                "ext": {"1794460900": "Recommend"},
                "name": "pc01",
                "retcode": 0,
                "title": "pc01"
            }
        ],
        "retcode": 0
    }
    
    print("📋 使用您提供的解密数据:")
    print(f"   返回码: {existing_dispatch_data['retcode']}")
    print(f"   服务器数量: {len(existing_dispatch_data['region_list'])}")
    
    print("\n📡 可用服务器:")
    for region in existing_dispatch_data["region_list"]:
        name = region["name"]
        title = region["title"]
        ext = region.get("ext", {})
        status = ext.get("1794460900", "Normal") if ext else "Normal"
        print(f"   • {name}: {title} ({status})")
    
    print(f"\n🎯 推荐服务器: pc01 (标记为 Recommend)")
    
    # 构建下一步的 gameserver URL
    pc01_info = next(r for r in existing_dispatch_data["region_list"] if r["name"] == "pc01")
    
    import time
    timestamp = int(time.time())
    gameserver_url = f"{pc01_info['dispatch_url']}?version=8.3.0_gf_pc&t={timestamp}&token=a9366694c30364d479dd142f9cf0de54"
    
    print(f"\n🔗 下一步查询 URL:")
    print(f"   {gameserver_url}")
    
    print(f"\n💡 使用方法:")
    print(f"   1. 访问上述 URL 获取加密响应")
    print(f"   2. 使用相同的解密工具解密响应")
    print(f"   3. 从响应中提取 asset_bundle_url_list")
    print(f"   4. 构建 pkg_version URL 进行下载")


if __name__ == "__main__":
    print("🎮 miHoYo 游戏资源获取流程演示")
    print("=" * 60)
    print()
    print("选择演示模式:")
    print("1. 完整流程演示 (需要网络连接)")
    print("2. 使用现有数据演示 (离线)")
    print()
    
    try:
        choice = input("请选择 (1 或 2): ").strip()
        
        if choice == "1":
            result = asyncio.run(demo_complete_flow())
        elif choice == "2":
            result = asyncio.run(demo_with_existing_data())
        else:
            print("无效选择")
            result = 1
            
        exit(result)
        
    except KeyboardInterrupt:
        print("\n\n用户取消操作")
        exit(0)
    except Exception as e:
        print(f"\n错误: {e}")
        exit(1)
