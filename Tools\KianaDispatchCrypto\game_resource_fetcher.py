#!/usr/bin/env python3
"""
Game Resource Fetcher
=====================

A tool for fetching game installation packages and update patches from miHoYo games
using the dispatch system.

Author: Collapse Launcher Team
License: MIT
"""

import json
import sys
import argparse
import asyncio
import aiohttp
import time
from typing import Dict, List, Optional, Tuple
from kiana_dispatch_crypto import KianaDispatchCrypto


class GameResourceFetcher:
    """
    Game resource fetcher for miHoYo games.
    
    This class implements the complete flow from dispatch response to
    game installation packages and update patches.
    """
    
    def __init__(self, dispatcher_key: str, version: str):
        """
        Initialize the resource fetcher.
        
        Args:
            dispatcher_key: The dispatcher key from game configuration
            version: Game version in format "major.minor"
        """
        self.crypto = KianaDispatchCrypto(dispatcher_key, version)
        self.version = version
        self.session = None
    
    async def __aenter__(self):
        """Async context manager entry."""
        self.session = aiohttp.ClientSession()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        if self.session:
            await self.session.close()
    
    async def fetch_dispatch_response(self, dispatch_url: str, channel: str = "gf_pc",
                                    token: str = None) -> Dict:
        """
        Fetch and decrypt dispatch response.

        Args:
            dispatch_url: Base dispatch URL
            channel: Channel name
            token: Authentication token (auto-detected if None)

        Returns:
            Decrypted dispatch response
        """
        # Auto-detect token based on server URL if not provided
        if token is None:
            if "osglobal" in dispatch_url or "honkaiimpact3.com" in dispatch_url:
                # Southeast Asia server
                token = "82cb3ccb0be3ee59d8b51e2139c18914"
                print(f"🌏 检测到东南亚服，使用对应 token")
            else:
                # China server (default)
                token = "a9366694c30364d479dd142f9cf0de54"
                print(f"🇨🇳 检测到国服，使用对应 token")

        timestamp = int(time.time())
        full_version = f"{self.version}.0"

        # Add language parameter for SEA servers
        if "osglobal" in dispatch_url:
            query_params = f"?version={full_version}_{channel}&t={timestamp}&lang=EN&token={token}"
        else:
            query_params = f"?version={full_version}_{channel}&t={timestamp}&token={token}"

        full_url = dispatch_url + query_params
        
        print(f"Fetching dispatch from: {full_url}")
        
        async with self.session.get(full_url) as response:
            if response.status != 200:
                raise Exception(f"HTTP {response.status}: {await response.text()}")
            
            encrypted_data = await response.text()
            return self.crypto.process_response(encrypted_data)
    
    async def fetch_gameserver_info(self, dispatch_data: Dict, server_name: str = None,
                                  channel: str = "gf_pc",
                                  token: str = None) -> Dict:
        """
        Fetch gameserver information.

        Args:
            dispatch_data: Dispatch response data
            server_name: Target server name (auto-detected if None)
            channel: Channel name
            token: Authentication token (auto-detected if None)

        Returns:
            Gameserver response data
        """
        # Auto-detect server name based on available servers
        available_servers = [r.get("name") for r in dispatch_data.get("region_list", [])]

        if server_name is None:
            # Auto-detect based on channel
            if channel == "os_pc":
                # SEA server priority
                for preferred in ["overseas01", "sea01", "global01"]:
                    if preferred in available_servers:
                        server_name = preferred
                        break
            else:
                # CN server priority
                for preferred in ["pc01", "android01", "ios01"]:
                    if preferred in available_servers:
                        server_name = preferred
                        break

            if server_name is None and available_servers:
                server_name = available_servers[0]  # Use first available

        print(f"🎯 目标服务器: {server_name}")
        print(f"📡 可用服务器: {', '.join(available_servers)}")

        # Find target server
        server_info = None
        for region in dispatch_data.get("region_list", []):
            if region.get("name") == server_name:
                server_info = region
                break

        if not server_info:
            raise Exception(f"Server '{server_name}' not found. Available: {', '.join(available_servers)}")

        # Auto-detect token if not provided
        if token is None:
            if channel == "os_pc" or "overseas" in server_name:
                token = "82cb3ccb0be3ee59d8b51e2139c18914"
            else:
                token = "a9366694c30364d479dd142f9cf0de54"
        
        # Build gameserver URL
        timestamp = int(time.time())
        full_version = f"{self.version}.0"

        # Add language parameter for SEA servers
        if channel == "os_pc" or "overseas" in server_name:
            query_params = f"?version={full_version}_{channel}&t={timestamp}&lang=EN&token={token}"
        else:
            query_params = f"?version={full_version}_{channel}&t={timestamp}&token={token}"

        gameserver_url = server_info["dispatch_url"] + query_params
        
        print(f"Fetching gameserver info from: {gameserver_url}")
        
        async with self.session.get(gameserver_url) as response:
            if response.status != 200:
                raise Exception(f"HTTP {response.status}: {await response.text()}")
            
            encrypted_data = await response.text()
            return self.crypto.process_response(encrypted_data)
    
    def extract_resource_urls(self, gameserver_data: Dict) -> Tuple[List[str], List[str]]:
        """
        Extract resource URLs from gameserver response.
        
        Args:
            gameserver_data: Gameserver response data
            
        Returns:
            Tuple of (asset_bundle_urls, external_resource_urls)
        """
        asset_bundle_urls = gameserver_data.get("asset_bundle_url_list", [])
        external_resource_urls = gameserver_data.get("ex_resource_url_list", [])
        
        return asset_bundle_urls, external_resource_urls
    
    def build_pkg_version_url(self, base_url: str) -> str:
        """
        Build pkg_version file URL.
        
        Args:
            base_url: Base asset bundle URL
            
        Returns:
            Complete pkg_version URL
        """
        return f"{base_url.rstrip('/')}/pkg_version"
    
    def build_cache_urls(self, base_url: str) -> Dict[str, str]:
        """
        Build cache asset URLs.
        
        Args:
            base_url: Base asset bundle URL
            
        Returns:
            Dictionary of cache type to URL mappings
        """
        base_url = base_url.rstrip('/')
        return {
            "data": f"{base_url}/data/editor_compressed/DataVersion.unity3d",
            "event": f"{base_url}/event/editor_compressed/ResourceVersion.unity3d",
            "ai": f"{base_url}/ai/editor_compressed/ResourceVersion.unity3d"
        }
    
    async def fetch_pkg_version(self, pkg_version_url: str) -> Optional[str]:
        """
        Fetch pkg_version file content.
        
        Args:
            pkg_version_url: pkg_version file URL
            
        Returns:
            pkg_version content or None if not found
        """
        try:
            print(f"Fetching pkg_version from: {pkg_version_url}")
            async with self.session.get(pkg_version_url) as response:
                if response.status == 200:
                    return await response.text()
                else:
                    print(f"pkg_version not found (HTTP {response.status})")
                    return None
        except Exception as e:
            print(f"Error fetching pkg_version: {e}")
            return None
    
    async def get_complete_resource_info(self, dispatch_url: str, server_name: str = "pc01") -> Dict:
        """
        Get complete resource information for a game.
        
        Args:
            dispatch_url: Base dispatch URL
            server_name: Target server name
            
        Returns:
            Complete resource information
        """
        # Step 1: Get dispatch response
        dispatch_data = await self.fetch_dispatch_response(dispatch_url, "os_pc")
        
        # Step 2: Get gameserver info
        gameserver_data = await self.fetch_gameserver_info(dispatch_data, server_name, "os_pc")
        
        # Step 3: Extract resource URLs
        asset_bundle_urls, external_resource_urls = self.extract_resource_urls(gameserver_data)
        
        if not asset_bundle_urls:
            raise Exception("No asset bundle URLs found in gameserver response")
        
        base_url = asset_bundle_urls[0]
        
        # Step 4: Build various resource URLs
        pkg_version_url = self.build_pkg_version_url(base_url)
        cache_urls = self.build_cache_urls(base_url)
        
        # Step 5: Try to fetch pkg_version
        pkg_version_content = await self.fetch_pkg_version(pkg_version_url)
        
        return {
            "dispatch_info": {
                "available_servers": [
                    {"name": r.get("name"), "title": r.get("title")} 
                    for r in dispatch_data.get("region_list", [])
                ],
                "selected_server": server_name
            },
            "gameserver_info": gameserver_data,
            "resource_urls": {
                "asset_bundle_urls": asset_bundle_urls,
                "external_resource_urls": external_resource_urls,
                "base_url": base_url
            },
            "download_urls": {
                "pkg_version": pkg_version_url,
                "cache_assets": cache_urls
            },
            "pkg_version_content": pkg_version_content,
            "next_steps": {
                "traditional_install": [
                    "1. Download pkg_version file",
                    "2. Parse pkg_version to get file list",
                    "3. Download individual game files",
                    "4. Extract and install"
                ],
                "sophon_install": [
                    "1. Use LauncherResourceChunksURL from game config",
                    "2. Get manifest from Sophon API",
                    "3. Download chunks in parallel",
                    "4. Assemble files from chunks"
                ]
            }
        }


async def main():
    """Main CLI interface."""
    parser = argparse.ArgumentParser(
        description="Game Resource Fetcher - Get installation packages and updates from miHoYo games",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Get complete resource info for Honkai Impact 3 CN
  python game_resource_fetcher.py -k bgyzmmhy -v 8.3 -u "https://global1.bh3.com/query_dispatch" -s pc01
  
  # Get info for different server
  python game_resource_fetcher.py -k bgyzmmhy -v 8.3 -u "https://global1.bh3.com/query_dispatch" -s android01
        """
    )
    
    parser.add_argument('-k', '--key', required=True,
                       help='Dispatcher key (e.g., bgyzmmhy)')
    parser.add_argument('-v', '--version', required=True,
                       help='Game version (e.g., 8.3)')
    parser.add_argument('-u', '--url', required=True,
                       help='Dispatch URL')
    parser.add_argument('-s', '--server', default='pc01',
                       help='Target server name (default: pc01)')
    parser.add_argument('-o', '--output',
                       help='Output file path')
    
    args = parser.parse_args()
    
    try:
        async with GameResourceFetcher(args.key, args.version) as fetcher:
            result = await fetcher.get_complete_resource_info(args.url, args.server)
            
            output = json.dumps(result, indent=2, ensure_ascii=False)
            
            if args.output:
                with open(args.output, 'w', encoding='utf-8') as f:
                    f.write(output)
                print(f"Result saved to {args.output}")
            else:
                print(output)
        
        return 0
        
    except Exception as e:
        print(f"Error: {e}", file=sys.stderr)
        return 1


if __name__ == '__main__':
    sys.exit(asyncio.run(main()))
