# Quick Start Guide

## Installation

1. **Install Python 3.7+** from [python.org](https://python.org)

2. **Install dependencies:**
   ```bash
   pip install -r requirements.txt
   ```

## Quick Test

Run the test suite to verify everything works:

```bash
# Windows
python test_crypto.py

# Linux/macOS  
python3 test_crypto.py
```

## Basic Usage

### Decrypt the Sample Response

```bash
# Windows
python kiana_dispatch_crypto.py decrypt -k bgyzmmhy -v 8.3 -f examples/sample_encrypted_response.txt

# Linux/macOS
python3 kiana_dispatch_crypto.py decrypt -k bgyzmmhy -v 8.3 -f examples/sample_encrypted_response.txt
```

### Encrypt Sample JSON

```bash
# Windows
python kiana_dispatch_crypto.py encrypt -k bgyzmmhy -v 8.3 -f examples/sample_json_data.json

# Linux/macOS
python3 kiana_dispatch_crypto.py encrypt -k bgyzmmhy -v 8.3 -f examples/sample_json_data.json
```

## Interactive Mode

### Windows
```bash
run_crypto.bat
```

### Linux/macOS
```bash
chmod +x run_crypto.sh
./run_crypto.sh
```

## Common Game Configurations

### Honkai Impact 3rd CN
- **Dispatcher Key:** `bgyzmmhy`
- **Version Format:** `8.3` (major.minor)

### Genshin Impact
- **Dispatcher Key:** `(varies by region)`
- **Version Format:** `4.2` (major.minor)

### Honkai: Star Rail
- **Dispatcher Key:** `(varies by region)`
- **Version Format:** `1.5` (major.minor)

## Example Commands

```bash
# Decrypt with direct input
python kiana_dispatch_crypto.py decrypt -k bgyzmmhy -v 8.3 -d "oG7PvQY0q1ZqrCiH..."

# Encrypt with direct input
python kiana_dispatch_crypto.py encrypt -k bgyzmmhy -v 8.3 -j '{"retcode":0,"message":"OK"}'

# Auto-detect data type
python kiana_dispatch_crypto.py auto -k bgyzmmhy -v 8.3 -d "some_data"

# Save output to file
python kiana_dispatch_crypto.py decrypt -k bgyzmmhy -v 8.3 -f input.txt -o output.json
```

## Troubleshooting

### "ModuleNotFoundError: No module named 'Crypto'"
```bash
pip install pycryptodome
```

### "Decryption failed"
- Check dispatcher key is correct
- Verify game version format (major.minor only)
- Ensure encrypted data is complete and not truncated

### "Invalid JSON"
- Data might be plain JSON (use auto-detect mode)
- Check if decryption was successful

## Need Help?

1. Run the test suite: `python test_crypto.py`
2. Check the full README.md for detailed documentation
3. Use interactive mode for guided usage
