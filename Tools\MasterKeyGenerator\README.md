# Master Key Generator Tool

这个工具用于生成CollapseLauncher所需的master key配置文件和stamp文件。

## 功能

- 生成RSA密钥对
- 创建config_master.json文件
- 生成对应的stamp条目
- 支持自定义密钥长度和配置参数
- 自动验证生成的配置文件
- 跨平台支持 (Windows/Linux/macOS)

## 快速开始

### Windows用户
```cmd
generate.bat
```

### Linux/macOS用户
```bash
chmod +x generate.sh
./generate.sh
```

### 手动使用Python脚本

```bash
python generate_master_key.py [options]
```

## 命令行选项

- `--key-size`: RSA密钥长度 (默认: 1024)
- `--bit-size`: BitSize字段值 (默认: 128)
- `--output-dir`: 输出目录 (默认: ./output)
- `--config-version`: 配置版本 (默认: "3.1.0")
- `--use-serve-v3`: 使用ServeV3格式 (默认: True)
- `--no-serve-v3`: 使用plain DER格式而不是ServeV3

## 使用示例

```bash
# 使用默认参数生成 (ServeV3格式)
python generate_master_key.py

# 生成plain DER格式
python generate_master_key.py --no-serve-v3

# 自定义参数生成
python generate_master_key.py --key-size 2048 --bit-size 256 --output-dir ./keys

# 生成用于生产环境的密钥 (ServeV3格式)
python generate_master_key.py --key-size 2048 --bit-size 256 --config-version "3.2.0"

# 生成用于生产环境的密钥 (DER格式)
python generate_master_key.py --key-size 2048 --bit-size 256 --config-version "3.2.0" --no-serve-v3
```

## 输出文件

生成的文件将保存在指定的输出目录中：

- `config_master.json`: Master key配置文件 (用于CollapseLauncher)
- `stamp_entry.json`: 对应的stamp条目 (用于metadata管理)
- `private_key.pem`: RSA私钥文件 (用于备份和恢复)
- `public_key.pem`: RSA公钥文件 (用于验证和测试)

## 配置文件格式

### config_master.json

#### ServeV3格式 (默认)
```json
{
  "Key": "Q29sbGFwc2UBAAAAAAAAAGICAAAAAAAAXgIAAAAAAACPLoE...",
  "BitSize": 128,
  "Hash": 4849480174666812961
}
```

#### Plain DER格式
```json
{
  "Key": "MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQC...",
  "BitSize": 128,
  "Hash": 1234567890123456789
}
```

**Key字段格式说明**：
- **ServeV3格式**: 包含32字节头部 + DER数据，支持压缩和加密标志
- **DER格式**: 直接的PKCS8 DER编码RSA私钥，Base64编码

### stamp_entry.json
```json
{
  "LastUpdated": 20240509063558,
  "MetadataPath": "config_master.json",
  "MetadataType": "MasterKey",
  "MetadataInclude": true,
  "PresetConfigVersion": "3.1.0"
}
```

## 验证工具

使用内置的验证工具来检查生成的配置文件：

```bash
# 验证整个目录
python validate_config.py ./output

# 验证单个配置文件
python validate_config.py ./output/config_master.json --type config

# 验证stamp条目
python validate_config.py ./output/stamp_entry.json --type stamp
```

## 依赖要求

- Python 3.7+
- cryptography >= 3.4.8

安装依赖：
```bash
pip install -r requirements.txt
```

## 安全注意事项

1. **私钥保护**: 生成的私钥文件包含敏感信息，请妥善保管
2. **备份**: 建议备份生成的所有文件，特别是私钥文件
3. **权限**: 确保输出目录有适当的访问权限
4. **版本管理**: 不要将私钥文件提交到版本控制系统

## 故障排除

### 常见问题

1. **Python未找到**: 确保Python 3.7+已安装并在PATH中
2. **依赖安装失败**: 尝试使用 `pip install --upgrade pip` 更新pip
3. **权限错误**: 确保对输出目录有写入权限
4. **验证失败**: 检查生成的文件是否完整，重新生成如有必要

### 日志和调试

工具会输出详细的生成和验证信息，如遇问题请查看控制台输出。
