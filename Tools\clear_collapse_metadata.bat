@echo off
REM Collapse Launcher 元数据缓存清理工具
REM =====================================

echo 🧹 Collapse Launcher 元数据缓存清理工具
echo ========================================
echo.

REM 检查是否以管理员身份运行
net session >nul 2>&1
if %errorLevel% == 0 (
    echo ✓ 检测到管理员权限
) else (
    echo ⚠️  建议以管理员身份运行此脚本
)
echo.

REM 查找 Collapse Launcher 进程
tasklist /FI "IMAGENAME eq CollapseLauncher.exe" 2>NUL | find /I /N "CollapseLauncher.exe">NUL
if "%ERRORLEVEL%"=="0" (
    echo ❌ 检测到 Collapse Launcher 正在运行
    echo    请先关闭 Collapse Launcher 再运行此脚本
    echo.
    pause
    exit /b 1
) else (
    echo ✓ Collapse Launcher 未运行
)
echo.

REM 查找可能的 Collapse Launcher 安装位置
set "COLLAPSE_PATHS="
set "FOUND_PATHS="

REM 常见安装位置
set "SEARCH_PATHS=C:\Program Files\CollapseLauncher"
set "SEARCH_PATHS=%SEARCH_PATHS% C:\Program Files (x86)\CollapseLauncher"
set "SEARCH_PATHS=%SEARCH_PATHS% %USERPROFILE%\Desktop\Collapse"
set "SEARCH_PATHS=%SEARCH_PATHS% %USERPROFILE%\Downloads\CollapseLauncher"
set "SEARCH_PATHS=%SEARCH_PATHS% D:\CollapseLauncher"
set "SEARCH_PATHS=%SEARCH_PATHS% E:\CollapseLauncher"

echo 🔍 搜索 Collapse Launcher 安装位置...

for %%P in (%SEARCH_PATHS%) do (
    if exist "%%P\CollapseLauncher.exe" (
        echo    ✓ 找到: %%P
        if defined FOUND_PATHS (
            set "FOUND_PATHS=%FOUND_PATHS%;%%P"
        ) else (
            set "FOUND_PATHS=%%P"
        )
    )
)

if not defined FOUND_PATHS (
    echo    ❌ 未找到 Collapse Launcher 安装目录
    echo.
    echo 请手动指定 Collapse Launcher 安装目录:
    set /p "MANUAL_PATH=输入完整路径: "
    
    if exist "!MANUAL_PATH!\CollapseLauncher.exe" (
        set "FOUND_PATHS=!MANUAL_PATH!"
        echo    ✓ 验证成功: !MANUAL_PATH!
    ) else (
        echo    ❌ 指定路径无效或不包含 CollapseLauncher.exe
        pause
        exit /b 1
    )
)
echo.

REM 处理找到的路径
setlocal enabledelayedexpansion
set "COUNT=0"
for %%P in ("%FOUND_PATHS:;=" "%") do (
    set /a COUNT+=1
    set "PATH_!COUNT!=%%~P"
)

if %COUNT% GTR 1 (
    echo 找到多个 Collapse Launcher 安装:
    for /L %%i in (1,1,%COUNT%) do (
        echo    %%i. !PATH_%%i!
    )
    echo.
    set /p "CHOICE=请选择要清理的安装 (1-%COUNT%): "
    
    if !CHOICE! GEQ 1 if !CHOICE! LEQ %COUNT% (
        set "SELECTED_PATH=!PATH_%CHOICE%!"
    ) else (
        echo 无效选择
        pause
        exit /b 1
    )
) else (
    set "SELECTED_PATH=%FOUND_PATHS%"
)

echo 📂 选择的路径: %SELECTED_PATH%
echo.

REM 检查元数据文件夹
set "METADATA_PATH=%SELECTED_PATH%\_metadatav3"
set "SOPHON_CACHE_PATH=%USERPROFILE%\AppData\LocalLow\CollapseLauncher\_sophonMetadataCache"

echo 🔍 检查缓存文件夹...

if exist "%METADATA_PATH%" (
    echo    ✓ 找到元数据缓存: %METADATA_PATH%
    set "HAS_METADATA=1"
) else (
    echo    ○ 元数据缓存不存在: %METADATA_PATH%
    set "HAS_METADATA=0"
)

if exist "%SOPHON_CACHE_PATH%" (
    echo    ✓ 找到 Sophon 缓存: %SOPHON_CACHE_PATH%
    set "HAS_SOPHON=1"
) else (
    echo    ○ Sophon 缓存不存在: %SOPHON_CACHE_PATH%
    set "HAS_SOPHON=0"
)
echo.

if %HAS_METADATA%==0 if %HAS_SOPHON%==0 (
    echo ✅ 没有找到需要清理的缓存文件
    echo.
    pause
    exit /b 0
)

REM 确认清理
echo ⚠️  即将清理以下缓存:
if %HAS_METADATA%==1 echo    • 元数据缓存: %METADATA_PATH%
if %HAS_SOPHON%==1 echo    • Sophon 缓存: %SOPHON_CACHE_PATH%
echo.
echo 这将删除所有缓存的游戏配置文件，启动器将重新下载。
echo.
set /p "CONFIRM=确认清理? (y/N): "

if /I not "%CONFIRM%"=="y" if /I not "%CONFIRM%"=="yes" (
    echo 操作已取消
    pause
    exit /b 0
)
echo.

REM 执行清理
echo 🧹 开始清理缓存...

if %HAS_METADATA%==1 (
    echo    清理元数据缓存...
    rmdir /S /Q "%METADATA_PATH%" 2>nul
    if exist "%METADATA_PATH%" (
        echo    ❌ 清理元数据缓存失败
    ) else (
        echo    ✓ 元数据缓存清理完成
    )
)

if %HAS_SOPHON%==1 (
    echo    清理 Sophon 缓存...
    rmdir /S /Q "%SOPHON_CACHE_PATH%" 2>nul
    if exist "%SOPHON_CACHE_PATH%" (
        echo    ❌ 清理 Sophon 缓存失败
    ) else (
        echo    ✓ Sophon 缓存清理完成
    )
)

echo.
echo ✅ 缓存清理完成！
echo.
echo 📝 下次启动 Collapse Launcher 时:
echo    • 启动器会自动重新下载元数据
echo    • 可能需要稍等片刻进行初始化
echo    • 如果仍有问题，请检查网络连接
echo.

REM 询问是否启动 Collapse Launcher
set /p "START_LAUNCHER=是否现在启动 Collapse Launcher? (y/N): "

if /I "%START_LAUNCHER%"=="y" if /I "%START_LAUNCHER%"=="yes" (
    echo 启动 Collapse Launcher...
    start "" "%SELECTED_PATH%\CollapseLauncher.exe"
) else (
    echo 请手动启动 Collapse Launcher
)

echo.
pause
