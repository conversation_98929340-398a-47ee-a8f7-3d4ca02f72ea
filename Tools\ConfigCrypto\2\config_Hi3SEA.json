{"ProfileName": "Hi3SEA", "LauncherType": "HoYoPlay", "GameChannel": "Stable", "IsExperimental": false, "ZoneName": "Southeast Asia", "ZoneFullname": "Honkai Impact 3 (Southeast Asia)", "ZoneDescription": "Honkai Impact 3 - In this Honkai-corrupted world, the Valkyries, brave girls with Honkai resistance, have been fighting for all that is beautiful in the world.", "ZoneURL": "https://honkaiimpact3.hoyoverse.com/asia/en-us/fab", "ZoneLogoURL": "metadata/game_logos/logo_honkai.png", "ZonePosterURL": "metadata/game_posters/poster_honkai.png", "InstallRegistryLocation": "HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\Honkai Impact 3", "DefaultGameLocation": "C:\\Program Files\\Honkai Impact 3 sea", "ConfigRegistryLocation": "Software\\miHoYo\\Honkai Impact 3", "BetterHi3LauncherVerInfoReg": "VersionInfoSEA", "FallbackLanguage": "en", "GameDirectoryName": "Games", "GameExecutableName": "BH3.exe", "GameType": "Honkai", "VendorType": "miHoYo", "LauncherID": 9, "ChannelID": 1, "SubChannelID": 1, "GameSupportedLanguages": ["Chinese", "English", "Vietnamese", "Thai", "Indonesian"], "GameDispatchArrayURL": ["Q29sbGFwc2UBAQAAAAAAAIAAAAAAAAAAMgAAAAAAAAAo8iULGnMVXB8vIlGgLOX+z/apCPayrCR70qKfc1KHL2bllBsy6d3ThNtgwhuxFprZOzaWTsRGU+MWzx/5Uf1bJhPXNMMo5m0dJnpGR8uTRTHfLOOoFsI8FjPuu8wIH5ZSz+0IvkacW2g6PpcPRl2wswxkOk/fMO8ervdheln+HA==", "Q29sbGFwc2UBAQAAAAAAAIAAAAAAAAAAMgAAAAAAAABHt6UOzYp0v7KSnE+Ak/CC6hljXNqxO1Tx+Hv1Pw29Km2WaHOK03SZKDyqDCq9PAEyNhVxmoR2GPKPcVIDFUbsYVGuCgDO6EBWgI5ntT04Qm0RMOYsDp/UQ8Q7RBzDZoeQ36ce/Tnck/iwLYeZoo930LMNBRs+qjZFrVs3Pf7Uwg=="], "GameDispatchChannelName": "Q29sbGFwc2UBAQAAAAAAAIAAAAAAAAAABQAAAAAAAAB5NKnFT7T65s+SYInp6ka8h/uu+A/h/QMbtyvkn08ZcOsKrnkqWooPWWzuK+dASp2o6kvdwBzO9or03v/vvJDOx+oF++3dqHICnEzu8RNQZ1qjESoNVmJgVuuY4fafNByasnkfwyRegS+YBqX5s/mBa6/s0F13hEnB20z9npFyxg==", "GameDispatchURLTemplate": "Q29sbGFwc2UBAQAAAAAAAIAAAAAAAAAARQAAAAAAAABusifBfjT4gDFDs98RXFrgzn//Y54JhGDROkwqwU/uCpldBBC9XPz2awqGgnXgWaS455rTN55VCpYcOROQRp2iAjfsbS45DZFiKT4iyOsRQ+XHfMPfOxl8ctO7UxlAiJfwkpc/gwjTfOKBcHonevKHKzKv35CLrT/i4u/tN6JMBg==", "GameGatewayURLTemplate": "Q29sbGFwc2UBAQAAAAAAAIAAAAAAAAAASwAAAAAAAABquwph0WA9pjhHmwbbE7sdtsrBf7fzo194qfVaYEHr/x8hedYW7nWrjvp0ArgmKk4cNiofDXlHD7wHBtg8tAuH5keJXQSNDKKCFRULH7pO9kriJlXueFF1g8Bc/M+L/TyNN+lNFFgTAvNNRkV8QB2mcZxQFJptotSJ1btrTMnX6Q==", "GameGatewayDefault": "Q29sbGFwc2UBAQAAAAAAAIAAAAAAAAAACgAAAAAAAACsCEAD+AX8DlZ/bzzo81f2mhpxKvKPosTOYaJu/OBJfKvyEp18zoPgBbTLDLtwzjSQXk1NlhJEap72c/SfS3dZuuOXWhRQwPk8HeLSL1okzEJrD638S6+6xBYD8ksRt14GqPr7PHMrnzVOCvaDt4lWGm6M5dvIRmKw2EYVfQLRlw==", "GameDefaultCVLanguage": "Japanese", "IsConvertible": true, "IsHideSocMedDesc": false, "ConvertibleTo": ["Global"], "LauncherSpriteURLMultiLang": true, "LauncherSpriteURLMultiLangFallback": "en-us", "LauncherSpriteURL": "Q29sbGFwc2UBAQAAAAAAAIAAAAAAAAAAfwAAAAAAAAB6xjKgLtJY15jucQihEIYsGTfbvol4YZj6QkuFUo0Oten49dkWtMU8//SvzhDN95agKzkUw80+HrXmUKEmOMoAqWofoFCx2Wf6EAyQhgKXhM5GGdbl5LcdAMXvSXZxpmzkSHMHYogOF1MrKTkjbjb0Mudu01VPMyAcrfIpprQkFA==", "LauncherCPSType": "Q29sbGFwc2UBAQAAAAAAAIAAAAAAAAAABgAAAAAAAACF/GohYBGoL64PWxGPrNi4cwMSiGOQaeCsrW+9kzWXih/dVZC00zyC6HIc7F/U1SnLJVa5zCLik93JVBQZXvniqTlukk+RMgi1iAR0PEuyMXgs+FMkY4sq3JoeuqvUfk6H0p70nuo7Enhwqb7EnGKVdY6O1A8tAXsc/6SSxtX6lw==", "LauncherNewsURL": "Q29sbGFwc2UBAQAAAAAAAIAAAAAAAAAAegAAAAAAAABFXVFERZe4069XjL0dwc7R0vimQl8ChDL7RMGACXbcroq4UxoFbR1elRX7PEKmv1JJyUO3SmMrJdIdipzzpZ/9xHB58CtcBlHq797MbaKkj9kcGzJufWlF3J/3Mo20v0Mf1IEXA64D1r2HxCTE+Fe0tcSBksUbBo/TVKVBF9Bzew==", "LauncherGameInfoDisplayURL": "Q29sbGFwc2UBAQAAAAAAAIAAAAAAAAAAYQAAAAAAAAAKYSfemPM3LfawViuwLjJRsg2UOm7it6OPWh5mjEIu0xpjsYHmdwLH2kV9y3mn2pvu33MwLjpyqKnylCj0ywzEMDx5TgRpS87wA6KnoBVDgr41DLEy+YobIOPVlylynC0DrxNOuoeJccDayS8yoAOs6nKsLhKAZQhGUJuD7HAs3g==", "LauncherResourceURL": "Q29sbGFwc2UBAQAAAAAAAIAAAAAAAAAAcQAAAAAAAACscmNF5OGpGShUawYoe6LJzpw1tG+33lWkwGIM8KPSs7eLXyBn5XnfJJmOErzEhKLAsgsJB33+DyAVh7rdDBqD3kBKUI3gNnjwSZ3NH/IqosKLz8rkx2QCKe11RiezHQrETweBJRmfTTWWCckMVKd9cjUeAqLvrBq7kqJzm2X6Nw==", "LauncherPluginURL": "Q29sbGFwc2UBAQAAAAAAAIAAAAAAAAAAcAAAAAAAAABIFGS4xGYrmZJiOjNm6OPMr9v4mzfo7xZX4jIIk3WcxJ9HGzAjkSuRLe9HPSOKt5OC4mUxlLK6hihLFfhxAawZN7Aub8ff0O4jfZ8Y5wMFTAfTLH25X2qxq9oVL7evq9LL4YRorxsYBveSVJIUmW0vIbzU7FQCg04SDpzE9lELHg==", "LauncherGameChannelSDKURL": "Q29sbGFwc2UBAQAAAAAAAIAAAAAAAAAAjAAAAAAAAAB11y696p82V4wuXCgqVV0whZHQY47IIwGkFIHCI2mzwAqjjrhdJm7RXbNwMfX4jeblBm0WN/WW+zWmih9xf2N0CXF24A/RPVPyY8ezMk2MjEfosMF4UayYi1nSR/FhPqUxPUP7cWP1VQGHYbSmwoD2L9dgnETJcFjMIFjDPrPkaQ==", "LauncherBizName": "Q29sbGFwc2UBAQAAAAAAAIAAAAAAAAAACgAAAAAAAABVW7wGSzd9euJiwI+l13Lnvcmnm/TIWjfFtI1zPSI0ak+cuv531WgF/eA9ZhfNG8BXoQRSwIli+CyCf8LAVTmbL/6ypzzdpqGnoyWelqk/nIdMD/tpsUHTd5n8YlegmKX3/wOxmX0w6SIdBzfAlaJTjOlj/2BG2ZZFmA/Jf//i6w==", "LauncherId": "Q29sbGFwc2UBAQAAAAAAAIAAAAAAAAAACgAAAAAAAACIwqSe/wDSGeL82p/oXJ5KjWROvfyazVaxyOUpy5z/pU14mmESOIbRrRTnTfZh+pqmdOwhlTKr8eOdtP1r8KZTYpG3zmizRYk2sBteFNiXoH3bPf6kSIN/7QOAiMEP0wJ+geSPh3eQ2jswf4NBy1nGwa1iykLpLMIcS1iyF1FJgw==", "LauncherGameId": "Q29sbGFwc2UBAQAAAAAAAIAAAAAAAAAACgAAAAAAAAA0M3tCRz1PXl4l32ctQ9Zb8G60tS1sCMI6prTTQxga3vFJSPJhrln4qzvw2RlJiP/Q0jmXMT6c+kYa8b4fw2hIQj1PjbxwXcb7DRAR4tZzQEowpic/5Dqez/R+SeJJ0PiB5+CGLudvBsoeqRTITDiwsaWe/f1PKqHB7R21TqXELw==", "DispatcherKey": "Q29sbGFwc2UBAQAAAAAAAIAAAAAAAAAACAAAAAAAAAAFbq8mdjN8bJOYpKJIJWz1sOFy03i0ayHGqH65yciJzOIKAQYi9PiPcC47jYNmopnBp3vP/moQxP6HQ/65twzM/8E3budj1aSZCMCLkQrTCFgpmaOEJk3wvcBQLcikIN40htVWtiuZfg1QeDdZC6qrQ/PD0G9n6dPADoTbGi+ahA==", "IsPluginUpdateEnabled": false, "IsRepairEnabled": true, "IsCacheUpdateEnabled": true, "InternalGameNameFolder": "Honkai Impact 3 sea", "InternalGameNameInConfig": "Honkai Impact 3", "GameDataTemplates": {}, "ZoneSteamAssets": {"Logo": {"URL": "metadata/game_logos/logo_honkai.png", "MD5": "cb2661b1f53553ce65cec84435f40b08"}, "Hero": {"URL": "metadata/steam_assets/game_heroes/hero_honkai.png", "MD5": "51c3e1bc9dc3e087684dddaa59555cdb"}, "Banner": {"URL": "metadata/steam_assets/game_banners/banner_honkai.png", "MD5": "a40c78e5c5a26990acf47df79be187ba"}, "Preview": {"URL": "metadata/steam_assets/game_previews/preview_honkai.png", "MD5": "53f3874d5dfba82e737b08cde3d62e9c"}}, "ApiGeneralUserAgent": "Mozilla/5.0", "ApiResourceUserAgent": "HYPContainer/********* (windows {0})", "ApiResourceAdditionalHeaders": {"sec-ch-ua": "\"Chromium\";v=\"102\"", "x-rpc-client_version": "*********", "sec-ch-ua-mobile": "?0", "sec-ch-ua-platform": "\"Windows\""}, "Hash": 3591293918402269630}