{"ProfileName": "Hi3SEA", "LauncherType": "HoYoPlay", "GameChannel": "Stable", "IsExperimental": false, "ZoneName": "Southeast Asia", "ZoneFullname": "Honkai Impact 3 (Southeast Asia)", "ZoneDescription": "Honkai Impact 3 - In this Honkai-corrupted world, the Valkyries, brave girls with Honkai resistance, have been fighting for all that is beautiful in the world.", "ZoneURL": "https://honkaiimpact3.hoyoverse.com/asia/en-us/fab", "ZoneLogoURL": "metadata/game_logos/logo_honkai.png", "ZonePosterURL": "metadata/game_posters/poster_honkai.png", "InstallRegistryLocation": "HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\Honkai Impact 3", "DefaultGameLocation": "C:\\Program Files\\Honkai Impact 3 sea", "ConfigRegistryLocation": "Software\\miHoYo\\Honkai Impact 3", "BetterHi3LauncherVerInfoReg": "VersionInfoSEA", "FallbackLanguage": "en", "GameDirectoryName": "Games", "GameExecutableName": "BH3.exe", "GameType": "Honkai", "VendorType": "miHoYo", "LauncherID": 9, "ChannelID": 1, "SubChannelID": 1, "GameSupportedLanguages": ["Chinese", "English", "Vietnamese", "Thai", "Indonesian"], "GameDispatchArrayURL": ["Q29sbGFwc2UBAQAAAAAAAC4AAAAAAAAAMgAAAAAAAACIKs/xKmQV079o4lCbqeGBvNsCU2e3FoUCaQFbw7TjIc/iIl/AJJ1AvBkGsUDxt60FOjrNvnz8g4+OB93SV2X7tdJHs7WARXLAI3bK27lucRAlzO3YlW45C8Y/mquS7Nl/ZjCA9IinA0j6cvTM23r6ymNTzI2AjwkaSdNUvOhn8w==", "Q29sbGFwc2UBAQAAAAAAAC4AAAAAAAAAMgAAAAAAAABRNobBH63v8AL9I4da0VTs/4JkbYPenyGL19FPXiu0y4olVL3r/PbGxvQKlxIV00TV8IunivV2wP1Yu3kXJerIA7B0u98Kz84uRe9in5Uav0IvuXDrM8NoLw+3BqrRHecLMTmAh1pLA9UMM2+pB7LXDZhoqIHk/uPnSGsbP+E63w=="], "GameDispatchChannelName": "Q29sbGFwc2UBAQAAAAAAAAkAAAAAAAAABQAAAAAAAABcO3XNpy4oVeORtxzJFOCRBfsvV+VFf57pbXRq3PItXMo30KrwaoDNpBWvENSKfP1sFQTYrYnSI5c8hmswVyonPYJKtjeLS7h/Mxu1Mi2InAWIH//ILkfVpkgA3UzBa2s19eAgDhIufQMGOobcCl8pv2isODMUYh7UjiUzYuRYSg==", "GameDispatchURLTemplate": "Q29sbGFwc2UBAQAAAAAAAEkAAAAAAAAARQAAAAAAAACfbIPzYw/H7AjEWe1+2DTYLhqH+9iLtZiX9hPwdGGe75W4g678TXGoW7WRv6i30WyUljXEukkeyNZT+EOhlhEtQjm05yYLdFhJL1709HG+aGkgIFZC0MzIQFbEUHOa83F/cVNSphfZzqdDVP8Pfhh0cOokebHF2bS/BF28goYhpA==", "GameGatewayURLTemplate": "Q29sbGFwc2UBAQAAAAAAAE8AAAAAAAAASwAAAAAAAACgtmcqjWxZnDuJT00TucNN21hlLssaPpoTyCQqANeWnpWrL2TJ/xwlJqRc/4gC0515q5QjJBtDEj8HerW27CWQQrQuJu/GaXn7XoN5tAhZ8Vmto7nAmnFfVNYvgvsrPveguWAc5DRAcQPP9YPz9v5vSHu7253O2HgLk+OHlxiHjQ==", "GameGatewayDefault": "Q29sbGFwc2UBAQAAAAAAAA4AAAAAAAAACgAAAAAAAAAU+dOhGgaSleGA0GHVdKgxJ9dr86yw7m/fTgWH5jALgpdPK2Rk97t7zRihmGczw9bQ6FixNp9OE2fNezuIMtwnlkO4GYlwlKcHZJBJ0+jzOrx36n6kfNVXoyzZDWv+UZR0Gbsn2ZzLaKjMYzq/9fsvMk4UdRVRaH2CGUGt9cSvYw==", "GameDefaultCVLanguage": "Japanese", "IsConvertible": true, "IsHideSocMedDesc": false, "ConvertibleTo": ["Global"], "LauncherSpriteURLMultiLang": true, "LauncherSpriteURLMultiLangFallback": "en-us", "LauncherSpriteURL": "Q29sbGFwc2UBAQAAAAAAAGgAAAAAAAAAfwAAAAAAAACKvNDSgyT357r9LnhF45ObHfPjjNtQNZlR3SiQyW5KwtvBnk+BXOTjdLeyBmvzhovEZ2Xd7sINMqHEWL1ui9zjbwDV7bndRoDZ0PUXGNM8U28CEdn4Xj38ivYqyVqDxeHP+el/QH32gd5CF24GvbprAbwfP/P3o115p7+qYfXKmQ==", "LauncherCPSType": "Q29sbGFwc2UBAQAAAAAAAAoAAAAAAAAABgAAAAAAAAAa4rgQF9+ERFj2kvsDV5SlSrzqV+g+Ity6Hy3znnh8g/Yi6WcjEDkrhBkg/rMLim4gEkr7rw3U6b5PvF5jRTgk0NsmQ1wcYtaG85HRoJUBAqPVh1RiEQln+GH/kGomSJCJLDHcu3F4vi9HoeCmm97QwtU9Aw7WFkVJBHpnq9A2kA==", "LauncherNewsURL": "Q29sbGFwc2UBAQAAAAAAAGQAAAAAAAAAegAAAAAAAACkTgUHkmBpiDPbeUh4bbdFP86Gm8xtmaJIHYJCBlebaSrxt1Chd5SVG+1XaE9iPG2F3KjISwip3vAWd2wH4NAj/yHCjw/Ide+j0pHWF5i11cujOU3z7OOYsKI64su9aSxpLqchYdja1iSLbLQEU1KGJYI+Y2Db3UxYMmMwK9UUFg==", "LauncherGameInfoDisplayURL": "Q29sbGFwc2UBAQAAAAAAAGQAAAAAAAAAYQAAAAAAAACCCH+gSLJRW+FcHN8voSV8ZSyNjOPkdG1qJx+4YTVTbwJWshTsXRDba7C0TNQWb2CMdCep0fyvebsXngGHF/6fPKorRZGhQhXRfBfhM3hXVV88sKOPPs2tMkvLkdRx9uA+ScEKsp6tn5iHxJD29oaEqxIxyTx4DZ3iiK1f1GRqxA==", "LauncherResourceURL": "Q29sbGFwc2UBAQAAAAAAAGEAAAAAAAAAcQAAAAAAAACB38yAruax8ddDcIyTqYZEB7eknTtmQLSt8azith6MkcZX+rjRd+r1DbwtIvWmxsOOYCz9lmEcVwT5BFKthDtUtq35MQ2KTletyCr5r954bfkjfN2LZCFz+uQ8a4JZ/fRUMM/daH2uakic+uRAGeSR7aXPA432QMWLn/JLrPq3xg==", "LauncherPluginURL": "Q29sbGFwc2UBAQAAAAAAAGIAAAAAAAAAcAAAAAAAAACJKTUFeXSQ4xpXT1IbQCuCTOmi4lBRz+xdrZrdT2701eqeM9VA57I05lRKfubEG3UYD5S7W/dAiTCRgIvsyU1TCTH5/sv25bJBdqZN2F3Uoidlw8xBM5iTAb+eD49sZbYhXjK/xz6Su2YK6IplzApaLOXEIHKfYVC4Kkpqlqkl3A==", "LauncherGameChannelSDKURL": "Q29sbGFwc2UBAQAAAAAAAHEAAAAAAAAAjAAAAAAAAACReNlJxxvrgCyFCp8T93VQ/MR0qjbTHMxepqHSe6Shq1h0l1GH0dMPQESBcEPLi9TarcGL604CRHeZFOsX0amfDGwJj+rkil6jR4EFoLz0W3J5306ysaB+/g/v254VhcYtemk/svWURon4vChx06ecYqTPWU2MOr9zwUOid0UeMw==", "LauncherBizName": "Q29sbGFwc2UBAQAAAAAAAA4AAAAAAAAACgAAAAAAAACa2jdgDfEcSduKayRC0smXbbZ75aAYOVIMDqIaHpefDG2Smt0IsIULKc2c0ebvbsoahjbrKICXme7vgqZaWLezxk+dVDQCRffqXAvZIcfn6dki9ALPF8urSd9AgQ9DO20CbN6WaYWwjnCCPe7047lX4Pm9F4Zrh6VbpMArHurIlA==", "LauncherId": "Q29sbGFwc2UBAQAAAAAAAA4AAAAAAAAACgAAAAAAAAAM4Q+DB6yujvKfx6J8v7uJIpEXu2S7MQXnhh9+1S/wIqNmefuOK0ZTgOxAYOqOVkNog+cJzJS7U9L+wWyl9jxpH1rbre8iUBNzjI7z9YN1IH15kXFvcI/s7wDtAhOf7rigG9CAJkhs5Uq2sgHTt/iJ6hteFfHgqyx33ATfEIxoww==", "LauncherGameId": "Q29sbGFwc2UBAQAAAAAAAA4AAAAAAAAACgAAAAAAAACShyj1FGe0C64+GCjlRB3vqhUP5G21RJTuW57cmuXUC0d1jmU7X6q0QKMCTZBUymmVsyvUgMmCpZmsPrONk93Z2pk46UzHP1AJwZ9klsofByZRSnjZLQzm6jp0novkp2mdRRPA0Wf4Ov1LC86whifMtS7WIrzGH96BZ9bfMYYDjQ==", "DispatcherKey": "Q29sbGFwc2UBAQAAAAAAAAwAAAAAAAAACAAAAAAAAAAvB9TbsxjHzUxqqHsyXaFG6AMPyD2LcgRU6oMGAEL8Qf2BAQfLLwzajqlaAHFfR/L8FaNHnv0fx3ca/gkBOiXTdnJ3UBMn0RoT8Z68l4xBWRb7f7O0WTkjj/0YN/IuQbmTAn/NyeCe5M4X/RDwuDHWExJNSVVLSbFHaRRPLmKETg==", "IsPluginUpdateEnabled": false, "IsRepairEnabled": true, "IsCacheUpdateEnabled": true, "InternalGameNameFolder": "Honkai Impact 3 sea", "InternalGameNameInConfig": "Honkai Impact 3", "GameDataTemplates": {}, "ZoneSteamAssets": {"Logo": {"URL": "metadata/game_logos/logo_honkai.png", "MD5": "cb2661b1f53553ce65cec84435f40b08"}, "Hero": {"URL": "metadata/steam_assets/game_heroes/hero_honkai.png", "MD5": "51c3e1bc9dc3e087684dddaa59555cdb"}, "Banner": {"URL": "metadata/steam_assets/game_banners/banner_honkai.png", "MD5": "a40c78e5c5a26990acf47df79be187ba"}, "Preview": {"URL": "metadata/steam_assets/game_previews/preview_honkai.png", "MD5": "53f3874d5dfba82e737b08cde3d62e9c"}}, "ApiGeneralUserAgent": "Mozilla/5.0", "ApiResourceUserAgent": "HYPContainer/1.3.3.182 (windows {0})", "ApiResourceAdditionalHeaders": {"sec-ch-ua": "\"Chromium\";v=\"102\"", "x-rpc-client_version": "1.3.3.182", "sec-ch-ua-mobile": "?0", "sec-ch-ua-platform": "\"Windows\""}, "Hash": 8222434483806862130}