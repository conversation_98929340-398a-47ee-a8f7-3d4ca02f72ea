# Collapse Launcher 游戏版本检测机制详解

## 概述

Collapse Launcher 使用多层次的版本检测机制来确定游戏的当前版本号，这个机制确保了启动器能够准确识别已安装的游戏版本，从而提供正确的更新和管理功能。

## 🔍 版本检测的核心流程

### 主要检测方法

Collapse Launcher 主要通过以下方式确定游戏版本：

1. **config.ini 文件检测**（主要方式）
2. **游戏可执行文件验证**
3. **游戏目录结构验证**
4. **注册表信息辅助**

## 📁 核心文件：config.ini

### 文件位置

```
游戏安装目录/
├── BH3.exe                    # 游戏可执行文件
├── config.ini                 # 版本配置文件 ⭐
├── BH3_Data/                  # 游戏数据目录
└── ...
```

### config.ini 结构

```ini
[General]
channel=1
cps=mihoyo
game_version=8.3.0             # 关键：游戏版本号 ⭐
sub_channel=1
sdk_version=
uapc={"channel":1,"sub_channel":1,"cps":"mihoyo"}

[launcher]
cps=mihoyo
channel=1
sub_channel=1
game_install_path=C:/Games/Honkai Impact 3 sea/Games
game_start_name=BH3.exe
is_first_exit=false
exit_type=2
```

## 🔧 版本检测实现

### 1. 主要检测逻辑

```csharp
protected virtual GameVersion? GameVersionInstalled
{
    get
    {
        // 检查 INI 文件是否有 game_version 键
        if (!GameIniVersion[DefaultIniVersionSection]
            .TryGetValue("game_version", out IniValue gameVersion))
        {
            return null;
        }

        // 获取游戏版本字符串
        string? val = gameVersion;

        // 如果为空，返回 null
        if (string.IsNullOrEmpty(val))
        {
            return null;
        }

        // 返回解析后的游戏版本
        return new GameVersion(val);
    }
}
```

### 2. 游戏安装检测

```csharp
public virtual string? FindGameInstallationPath(string path)
{
    // 尝试从可执行文件位置找到游戏路径
    string? basePath = TryFindGamePathFromExecutableAndConfig(path, 
                                                             GamePreset.GameExecutableName);

    // 如果可执行文件和版本配置不存在，返回 null
    if (string.IsNullOrEmpty(basePath))
    {
        return null;
    }

    // 检查 ini 文件是否有 "game_version" 值
    string iniPath = Path.Combine(basePath, ConfigFileName);
    return IsTryParseIniVersionExist(iniPath) ? basePath : null;
}
```

### 3. 多路径检测策略

```csharp
protected virtual string? TryFindGamePathFromExecutableAndConfig(string path, string? executableName)
{
    // 阶段 1: 检查根目录
    string targetPath = Path.Combine(path, executableName ?? string.Empty);
    string configPath = Path.Combine(path, ConfigFileName);
    FileInfo targetInfo = new FileInfo(targetPath);
    
    if (targetInfo is { Exists: true, Length: > 1 << 16 } && File.Exists(configPath))
    {
        return Path.GetDirectoryName(targetPath);
    }

    // 阶段 2: 检查启动器目录 + GameDirectoryName
    targetPath = Path.Combine(path, GamePreset.GameDirectoryName ?? "Games", executableName ?? string.Empty);
    configPath = Path.Combine(path, GamePreset.GameDirectoryName ?? "Games", ConfigFileName);
    targetInfo = new FileInfo(targetPath);
    
    if (targetInfo is { Exists: true, Length: > 1 << 16 } && File.Exists(configPath))
    {
        return Path.GetDirectoryName(targetPath);
    }

    return null;
}
```

## 🎯 检测优先级

### 1. 文件存在性检查

1. **可执行文件存在** (`BH3.exe`)
2. **文件大小验证** (> 64KB)
3. **config.ini 存在**
4. **game_version 字段存在且非空**

### 2. 版本验证流程

```mermaid
graph TD
    A[开始检测] --> B{可执行文件存在?}
    B -->|否| C[返回 null]
    B -->|是| D{文件大小 > 64KB?}
    D -->|否| C
    D -->|是| E{config.ini 存在?}
    E -->|否| C
    E -->|是| F{game_version 字段存在?}
    F -->|否| C
    F -->|是| G{版本号非空?}
    G -->|否| C
    G -->|是| H[返回版本号]
```

## 📊 版本号格式

### 支持的版本格式

Collapse Launcher 支持多种版本号格式：

```csharp
// 支持的格式示例
"8"           // 主版本
"8.3"         // 主版本.次版本
"8.3.0"       // 主版本.次版本.修订版
"8.3.0.1"     // 主版本.次版本.修订版.构建号
```

### 版本解析逻辑

```csharp
public GameVersion(string? version)
{
    if (!TryParse(version, out GameVersion? versionOut) || !versionOut.HasValue)
    {
        throw new ArgumentException($"Version should be in \"x\", \"x.x\", \"x.x.x\" or \"x.x.x.x\" format");
    }
    // 解析版本号各个部分
}
```

## 🔄 版本更新机制

### 1. 版本写入

```csharp
public void UpdateGameVersion(GameVersion? version, bool saveValue = true)
{
    GameIniVersionSection["game_version"] = version?.VersionString;
    if (saveValue)
    {
        SaveGameIni(GameIniVersionPath, GameIniVersion);
    }
}
```

### 2. 更新到最新版本

```csharp
public void UpdateGameVersionToLatest(bool saveValue = true)
{
    GameVersionInstalled = GameVersionAPI;  // 从 API 获取最新版本
    if (!saveValue)
    {
        return;
    }

    SaveGameIni(GameIniVersionPath, GameIniVersion);
    UpdateGameChannels();
    UpdatePluginVersions(PluginVersionsAPI);
}
```

## 🛠️ 特殊情况处理

### 1. 多区域游戏支持

对于原神等支持多区域的游戏：

```csharp
// 检查 CN 和 Bilibili 客户端
protected override string? TryFindGamePathFromExecutableAndConfig(string path, string? executableName)
{
    string? basePath = base.TryFindGamePathFromExecutableAndConfig(path, executableName);

    if (!string.IsNullOrEmpty(basePath))
    {
        return basePath;
    }

    // 尝试使用替代可执行文件名
    executableName = AlternativeExecName;
    return base.TryFindGamePathFromExecutableAndConfig(path, executableName);
}
```

### 2. 游戏厂商验证

```csharp
protected virtual bool IsGameVendorValid(string? executableName)
{
    // 检查 app.info 文件中的厂商信息
    string appInfoFilePath = Path.Combine(GameDirPath, $"{executableName}_Data", "app.info");
    // 验证厂商类型和游戏名称
    return VendorTypeProp.GameName == GamePreset.InternalGameNameInConfig;
}
```

## 📋 实际应用示例

### 崩坏3版本检测

```ini
# 崩坏3 config.ini 示例
[General]
channel=1
cps=mihoyo
game_version=8.3.0        # 当前版本
sub_channel=1
sdk_version=
```

### 原神版本检测

```ini
# 原神 config.ini 示例
[General]
channel=1
cps=mihoyo
game_version=4.2.0        # 当前版本
sub_channel=1
sdk_version=2.28.1
```

## 🔧 版本检测 API

### 公共接口

```csharp
public interface IGameVersionCheck
{
    /// <summary>
    /// 返回已安装游戏的版本
    /// 如果游戏未安装则返回 null
    /// </summary>
    GameVersion? GetGameExistingVersion();

    /// <summary>
    /// 检查游戏版本是否与 API 提供的版本匹配
    /// </summary>
    bool IsGameVersionMatch();
}
```

### 使用示例

```csharp
// 获取当前安装的游戏版本
GameVersion? installedVersion = GameVersionManager.GetGameExistingVersion();

// 获取 API 提供的最新版本
GameVersion? latestVersion = GameVersionManager.GetGameVersionApi();

// 检查是否需要更新
bool needsUpdate = !installedVersion?.IsMatch(latestVersion) ?? true;
```

## 💡 总结

Collapse Launcher 的版本检测机制具有以下特点：

### ✅ **可靠性**
- 多重验证确保检测准确性
- 文件存在性和大小验证
- 版本格式严格校验

### ✅ **兼容性**
- 支持多种版本号格式
- 兼容不同区域的游戏客户端
- 处理特殊安装情况

### ✅ **灵活性**
- 支持多路径检测
- 自动修复无效配置
- 版本更新自动化

### ✅ **准确性**
- 基于官方配置文件
- 与游戏内部版本系统一致
- 实时反映游戏状态

这种设计确保了 Collapse Launcher 能够准确识别和管理各种 miHoYo 游戏的版本，为用户提供可靠的游戏管理体验。
