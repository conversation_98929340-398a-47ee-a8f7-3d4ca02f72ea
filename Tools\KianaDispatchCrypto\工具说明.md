# KianaDispatch 加密解密工具

## 工具概述

这是一个用于加密和解密 miHoYo 游戏 KianaDispatch 响应的 Python 工具。基于 Collapse Launcher 的 `Hi3Helper.EncTool.Parser.KianaDispatch` 实现。

## 文件结构

```
KianaDispatchCrypto/
├── kiana_dispatch_crypto.py    # 主要的加密解密工具
├── game_resource_fetcher.py    # 游戏资源获取工具
├── 演示完整流程.py             # 完整流程演示脚本
├── test_crypto.py             # 测试脚本
├── run_crypto.bat             # Windows 交互式运行脚本
├── run_crypto.sh              # Linux/macOS 运行脚本
├── requirements.txt           # Python 依赖包
├── README.md                  # 详细英文文档
├── QUICKSTART.md             # 快速开始指南
├── 工具说明.md               # 中文说明文档（本文件）
└── examples/                  # 示例文件
    ├── sample_encrypted_response.txt  # 示例加密响应
    └── sample_json_data.json         # 示例 JSON 数据
```

## Windows 快速开始

### 1. 安装 Python

如果还没有安装 Python，请从 [python.org](https://python.org) 下载并安装 Python 3.7 或更高版本。

### 2. 安装依赖

在当前目录打开命令提示符（cmd）或 PowerShell，运行：

```cmd
pip install -r requirements.txt
```

### 3. 运行测试

验证工具是否正常工作：

```cmd
python test_crypto.py
```

### 4. 使用交互式界面

双击 `run_crypto.bat` 或在命令行运行：

```cmd
run_crypto.bat
```

## 基本用法

### 解密响应

```cmd
python kiana_dispatch_crypto.py decrypt -k bgyzmmhy -v 8.3 -f examples\sample_encrypted_response.txt
```

### 加密 JSON 数据

```cmd
python kiana_dispatch_crypto.py encrypt -k bgyzmmhy -v 8.3 -f examples\sample_json_data.json
```

### 自动检测数据类型

```cmd
python kiana_dispatch_crypto.py auto -k bgyzmmhy -v 8.3 -d "数据内容"
```

### 获取游戏服务器信息

```cmd
python kiana_dispatch_crypto.py gameserver -k bgyzmmhy -v 8.3 -d "dispatch响应数据" -s pc01
```

### 获取完整游戏资源信息

```cmd
python game_resource_fetcher.py -k bgyzmmhy -v 8.3 -u "https://global1.bh3.com/query_dispatch" -s pc01
```

### 演示完整流程

```cmd
python 演示完整流程.py
```

## 支持的游戏配置

### 崩坏3国服 (Hi3CN)
- **调度密钥 (DispatcherKey):** `bgyzmmhy`
- **版本格式:** `8.3` (主版本.次版本)
- **分发服务器:** `global1.bh3.com`
- **渠道名称:** `gf_pc`
- **默认网关:** `pc01`

### 崩坏3东南亚服 (Hi3SEA)
- **调度密钥 (DispatcherKey):** `bgyzmmhy`
- **版本格式:** `8.3` (主版本.次版本)
- **分发服务器:** `osglobal1.honkaiimpact3.com`
- **渠道名称:** `os_pc`
- **默认网关:** `overseas01`
- **语言支持:** 中文、英文、越南语、泰语、印尼语

### 原神
- **调度密钥:** 根据区域不同而变化
- **版本格式:** `4.2` (主版本.次版本)

### 崩坏：星穹铁道
- **调度密钥:** 根据区域不同而变化
- **版本格式:** `1.5` (主版本.次版本)

## 实际使用示例

### 解密国服响应数据

```cmd
python kiana_dispatch_crypto.py decrypt -k bgyzmmhy -v 8.3 -d "oG7PvQY0q1ZqrCiH6s+sz1gHfjY8hckVk5vCRUbXBt3v8/SOWjLo1y4eWPGQDflNFSStynuuBnqex38/pMz94JLVG5uyRlqh25etOw1P+kb..."
```

### 获取东南亚服资源信息

```cmd
python game_resource_fetcher.py -k bgyzmmhy -v 8.3 -u "https://osglobal1.honkaiimpact3.com/query_dispatch" -s overseas01
```

### 测试东南亚服

```cmd
python test_hi3_sea.py
```

### 加密自定义响应

```cmd
python kiana_dispatch_crypto.py encrypt -k bgyzmmhy -v 8.3 -j "{\"retcode\":0,\"message\":\"OK\"}"
```

## 工作原理

### 密钥生成过程
1. **组合**: 版本号 + 调度密钥 (例如: "8.3bgyzmmhy")
2. **哈希**: 计算组合字符串的 MD5 哈希值
3. **转换**: 将 MD5 哈希转换为小写十六进制字符串
4. **提取**: 使用前 32 字节作为 AES-256 密钥

### 解密流程
1. **Base64 解码**: 解码加密的响应数据
2. **AES 解密**: 使用 AES-256-ECB 模式解密数据
3. **移除填充**: 移除 PKCS7 填充（如果存在）
4. **解析 JSON**: 将解密后的字节转换为 JSON

### 加密流程
1. **JSON 编码**: 将数据转换为 JSON 字符串
2. **数据填充**: 添加 PKCS7 填充以匹配 AES 块大小
3. **AES 加密**: 使用 AES-256-ECB 模式加密数据
4. **Base64 编码**: 将结果编码为 Base64 字符串

## 常见问题

### "ModuleNotFoundError: No module named 'Crypto'"

```cmd
pip install pycryptodome
```

### "解密失败"

- 检查调度密钥是否正确
- 验证游戏版本格式（仅主版本.次版本）
- 确保加密数据完整且未被截断

### "无效的 JSON"

- 数据可能是纯 JSON（使用自动检测模式）
- 检查解密是否成功

## 命令行参数

- `-k, --key`: 游戏配置中的调度密钥（例如 "bgyzmmhy"）
- `-v, --version`: 游戏版本，格式为 "主版本.次版本"（例如 "8.3"）
- `-d, --data`: 输入数据字符串
- `-j, --json`: 要加密的 JSON 数据
- `-f, --file`: 输入文件路径
- `-o, --output`: 输出文件路径

## 安全说明

- 该加密使用 AES-256-ECB 模式
- 密钥从游戏版本和调度密钥派生
- 此工具仅用于教育和调试目的
- 请始终遵守游戏服务条款

## 技术支持

1. 运行测试套件: `python test_crypto.py`
2. 查看完整的 README.md 了解详细文档
3. 使用交互模式获得引导式使用体验

## 许可证

MIT 许可证 - 详见 Collapse Launcher 主项目
