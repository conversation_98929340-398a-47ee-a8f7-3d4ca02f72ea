#!/usr/bin/env python3
"""
游戏版本检测工具
===============

模拟 Collapse Launcher 的版本检测机制，用于检测和验证游戏安装版本。
"""

import os
import sys
import argparse
import configparser
from pathlib import Path
from typing import Optional, Dict, List, Tuple


class GameVersionDetector:
    """游戏版本检测器"""
    
    def __init__(self):
        self.config_filename = "config.ini"
        self.min_executable_size = 65536  # 64KB
    
    def detect_game_version(self, game_path: str, executable_name: str = "BH3.exe") -> Optional[str]:
        """
        检测游戏版本
        
        Args:
            game_path: 游戏路径
            executable_name: 可执行文件名
            
        Returns:
            游戏版本字符串，如果检测失败返回 None
        """
        
        print(f"🔍 开始检测游戏版本...")
        print(f"   游戏路径: {game_path}")
        print(f"   可执行文件: {executable_name}")
        print()
        
        # 步骤 1: 检查可执行文件
        if not self._check_executable(game_path, executable_name):
            return None
        
        # 步骤 2: 检查配置文件
        config_path = os.path.join(game_path, self.config_filename)
        if not self._check_config_file(config_path):
            return None
        
        # 步骤 3: 读取版本号
        version = self._read_game_version(config_path)
        if version:
            print(f"✅ 检测到游戏版本: {version}")
        else:
            print(f"❌ 无法读取游戏版本")
        
        return version
    
    def _check_executable(self, game_path: str, executable_name: str) -> bool:
        """检查可执行文件"""
        
        executable_path = os.path.join(game_path, executable_name)
        
        print(f"🔍 检查可执行文件: {executable_path}")
        
        # 检查文件是否存在
        if not os.path.exists(executable_path):
            print(f"   ❌ 可执行文件不存在")
            return False
        
        # 检查文件大小
        file_size = os.path.getsize(executable_path)
        if file_size <= self.min_executable_size:
            print(f"   ❌ 文件大小过小: {file_size} bytes (需要 > {self.min_executable_size})")
            return False
        
        print(f"   ✅ 可执行文件有效 (大小: {file_size:,} bytes)")
        return True
    
    def _check_config_file(self, config_path: str) -> bool:
        """检查配置文件"""
        
        print(f"🔍 检查配置文件: {config_path}")
        
        if not os.path.exists(config_path):
            print(f"   ❌ 配置文件不存在")
            return False
        
        print(f"   ✅ 配置文件存在")
        return True
    
    def _read_game_version(self, config_path: str) -> Optional[str]:
        """读取游戏版本"""
        
        print(f"🔍 读取游戏版本...")
        
        try:
            config = configparser.ConfigParser()
            config.read(config_path, encoding='utf-8')
            
            # 检查 [General] 段
            if 'General' not in config:
                print(f"   ❌ 配置文件中没有 [General] 段")
                return None
            
            # 检查 game_version 键
            if 'game_version' not in config['General']:
                print(f"   ❌ 配置文件中没有 game_version 键")
                return None
            
            version = config['General']['game_version'].strip()
            
            if not version:
                print(f"   ❌ game_version 值为空")
                return None
            
            print(f"   ✅ 读取到版本: {version}")
            return version
            
        except Exception as e:
            print(f"   ❌ 读取配置文件失败: {e}")
            return None
    
    def scan_game_installations(self, search_paths: List[str]) -> List[Tuple[str, str, str]]:
        """
        扫描游戏安装
        
        Args:
            search_paths: 搜索路径列表
            
        Returns:
            (游戏路径, 可执行文件, 版本) 的列表
        """
        
        print(f"🔍 扫描游戏安装...")
        print(f"   搜索路径: {len(search_paths)} 个")
        print()
        
        installations = []
        
        # 常见的游戏可执行文件名
        executable_names = [
            "BH3.exe",           # 崩坏3
            "GenshinImpact.exe", # 原神国际服
            "YuanShen.exe",      # 原神国服
            "StarRail.exe",      # 星穹铁道
            "ZenlessZoneZero.exe" # 绝区零
        ]
        
        for search_path in search_paths:
            if not os.path.exists(search_path):
                print(f"⚠️  路径不存在: {search_path}")
                continue
            
            print(f"🔍 搜索路径: {search_path}")
            
            # 搜索子目录
            for root, dirs, files in os.walk(search_path):
                for executable_name in executable_names:
                    if executable_name in files:
                        version = self.detect_game_version(root, executable_name)
                        if version:
                            installations.append((root, executable_name, version))
                            print(f"   ✅ 找到游戏: {executable_name} v{version}")
                            print(f"      路径: {root}")
                            print()
        
        return installations
    
    def create_sample_config(self, output_path: str, version: str = "8.3.0") -> bool:
        """创建示例配置文件"""
        
        print(f"🔧 创建示例配置文件...")
        print(f"   输出路径: {output_path}")
        print(f"   版本: {version}")
        
        config_content = f"""[General]
channel=1
cps=mihoyo
game_version={version}
sub_channel=1
sdk_version=
uapc={{"channel":1,"sub_channel":1,"cps":"mihoyo"}}

[launcher]
cps=mihoyo
channel=1
sub_channel=1
game_install_path={output_path.replace(os.sep, '/')}
game_start_name=BH3.exe
is_first_exit=false
exit_type=2
"""
        
        try:
            os.makedirs(os.path.dirname(output_path), exist_ok=True)
            
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(config_content)
            
            print(f"   ✅ 配置文件创建成功")
            return True
            
        except Exception as e:
            print(f"   ❌ 创建配置文件失败: {e}")
            return False
    
    def validate_version_format(self, version: str) -> bool:
        """验证版本号格式"""
        
        print(f"🔍 验证版本号格式: {version}")
        
        # 支持的格式: x, x.x, x.x.x, x.x.x.x
        parts = version.split('.')
        
        if len(parts) < 1 or len(parts) > 4:
            print(f"   ❌ 版本号部分数量无效: {len(parts)} (应为 1-4)")
            return False
        
        for i, part in enumerate(parts):
            if not part.isdigit():
                print(f"   ❌ 版本号第 {i+1} 部分不是数字: {part}")
                return False
        
        print(f"   ✅ 版本号格式有效")
        return True
    
    def display_config_info(self, config_path: str):
        """显示配置文件信息"""
        
        print(f"📋 配置文件信息: {config_path}")
        print("-" * 50)
        
        if not os.path.exists(config_path):
            print("❌ 配置文件不存在")
            return
        
        try:
            config = configparser.ConfigParser()
            config.read(config_path, encoding='utf-8')
            
            for section_name in config.sections():
                print(f"\n[{section_name}]")
                for key, value in config[section_name].items():
                    print(f"   {key} = {value}")
            
        except Exception as e:
            print(f"❌ 读取配置文件失败: {e}")


def main():
    """主函数"""
    
    parser = argparse.ArgumentParser(
        description="游戏版本检测工具 - 模拟 Collapse Launcher 的版本检测机制",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  # 检测指定路径的游戏版本
  python game_version_detector.py detect -p "C:/Games/Honkai Impact 3 sea/Games"
  
  # 扫描常见安装路径
  python game_version_detector.py scan
  
  # 创建示例配置文件
  python game_version_detector.py create -o "test_config.ini" -v "8.3.0"
  
  # 显示配置文件信息
  python game_version_detector.py info -c "config.ini"
        """
    )
    
    subparsers = parser.add_subparsers(dest='command', help='可用命令')
    
    # detect 命令
    detect_parser = subparsers.add_parser('detect', help='检测游戏版本')
    detect_parser.add_argument('-p', '--path', required=True,
                              help='游戏安装路径')
    detect_parser.add_argument('-e', '--executable', default='BH3.exe',
                              help='可执行文件名 (默认: BH3.exe)')
    
    # scan 命令
    scan_parser = subparsers.add_parser('scan', help='扫描游戏安装')
    scan_parser.add_argument('--paths', nargs='*',
                            default=['C:/Program Files', 'C:/Games', 'D:/Games'],
                            help='搜索路径列表')
    
    # create 命令
    create_parser = subparsers.add_parser('create', help='创建示例配置')
    create_parser.add_argument('-o', '--output', required=True,
                              help='输出配置文件路径')
    create_parser.add_argument('-v', '--version', default='8.3.0',
                              help='游戏版本 (默认: 8.3.0)')
    
    # info 命令
    info_parser = subparsers.add_parser('info', help='显示配置信息')
    info_parser.add_argument('-c', '--config', required=True,
                            help='配置文件路径')
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return 1
    
    detector = GameVersionDetector()
    
    try:
        if args.command == 'detect':
            version = detector.detect_game_version(args.path, args.executable)
            if version:
                print(f"\n🎉 检测成功!")
                print(f"   游戏路径: {args.path}")
                print(f"   可执行文件: {args.executable}")
                print(f"   游戏版本: {version}")
                
                # 验证版本格式
                if detector.validate_version_format(version):
                    print(f"   版本格式: ✅ 有效")
                else:
                    print(f"   版本格式: ❌ 无效")
            else:
                print(f"\n❌ 检测失败")
                return 1
        
        elif args.command == 'scan':
            installations = detector.scan_game_installations(args.paths)
            
            print(f"\n📊 扫描结果:")
            print(f"   找到 {len(installations)} 个游戏安装")
            
            if installations:
                print(f"\n📋 详细信息:")
                for i, (path, executable, version) in enumerate(installations, 1):
                    print(f"   {i}. {executable} v{version}")
                    print(f"      路径: {path}")
            else:
                print(f"   未找到任何游戏安装")
        
        elif args.command == 'create':
            if detector.create_sample_config(args.output, args.version):
                print(f"\n🎉 示例配置创建成功!")
                detector.display_config_info(args.output)
            else:
                return 1
        
        elif args.command == 'info':
            detector.display_config_info(args.config)
        
        return 0
        
    except Exception as e:
        print(f"\n❌ 错误: {e}")
        return 1


if __name__ == "__main__":
    exit(main())
