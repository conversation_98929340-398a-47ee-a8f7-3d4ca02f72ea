#!/usr/bin/env python3
"""
main.patches 配置生成工具
==========================

用于生成符合 HoYoPlay API 格式的 main.patches 配置文件。
"""

import json
import hashlib
import argparse
from typing import Dict, List, Optional
from datetime import datetime


class PatchConfigGenerator:
    """补丁配置生成器"""
    
    def __init__(self):
        self.config = {
            "retcode": 0,
            "message": "OK",
            "data": {
                "game_packages": []
            }
        }
    
    def create_package_details(self, 
                             url: str,
                             size: int,
                             decompressed_size: int,
                             md5: Optional[str] = None,
                             path: Optional[str] = None,
                             pkg_version_file: str = "pkg_version",
                             language: Optional[str] = None) -> Dict:
        """创建包详情"""
        
        # 如果没有提供MD5，生成一个示例MD5
        if md5 is None:
            md5 = hashlib.md5(url.encode()).hexdigest()
        
        package = {
            "url": url,
            "md5": md5,
            "size": str(size),
            "decompressed_size": str(decompressed_size)
        }
        
        if path:
            package["path"] = path
        
        if pkg_version_file != "pkg_version":
            package["pkg_version_file_name"] = pkg_version_file
        
        if language:
            package["language"] = language
            
        return package
    
    def create_version_section(self,
                             version: str,
                             game_packages: List[Dict],
                             audio_packages: Optional[List[Dict]] = None,
                             res_list_url: Optional[str] = None) -> Dict:
        """创建版本段"""
        
        section = {
            "version": version,
            "game_pkgs": game_packages
        }
        
        if audio_packages:
            section["audio_pkgs"] = audio_packages
        
        if res_list_url:
            section["res_list_url"] = res_list_url
            
        return section
    
    def add_game_package(self,
                        game_biz: str = "bh3_global",
                        game_id: str = "bxPTXSET5t",
                        main_version: str = "8.3.0",
                        main_game_packages: List[Dict] = None,
                        main_audio_packages: List[Dict] = None,
                        patches: List[Dict] = None,
                        preload_version: Optional[str] = None,
                        preload_game_packages: List[Dict] = None,
                        preload_audio_packages: List[Dict] = None,
                        preload_patches: List[Dict] = None):
        """添加游戏包配置"""
        
        if main_game_packages is None:
            main_game_packages = []
        if main_audio_packages is None:
            main_audio_packages = []
        if patches is None:
            patches = []
        
        # 构建主包配置
        main_config = {
            "major": self.create_version_section(
                main_version,
                main_game_packages,
                main_audio_packages
            )
        }
        
        if patches:
            main_config["patches"] = patches
        
        # 构建游戏包
        game_package = {
            "game": {
                "biz": game_biz,
                "id": game_id
            },
            "main": main_config
        }
        
        # 添加预下载配置
        if preload_version:
            preload_config = {
                "major": self.create_version_section(
                    preload_version,
                    preload_game_packages or [],
                    preload_audio_packages or []
                )
            }
            
            if preload_patches:
                preload_config["patches"] = preload_patches
                
            game_package["pre_download"] = preload_config
        
        self.config["data"]["game_packages"].append(game_package)
    
    def generate_example_config(self) -> Dict:
        """生成示例配置"""
        
        # 主版本游戏包
        main_game_pkg = self.create_package_details(
            url="https://autopatchcn.bh3.com/ptpublic/rel/20241201_8.3.0_full/BH3_v8.3.0_full.zip",
            size=15728640000,  # ~14.6GB
            decompressed_size=20971520000,  # ~19.5GB
            path="https://autopatchcn.bh3.com/ptpublic/rel/20241201_8.3.0_full/"
        )
        
        # 主版本语音包
        main_audio_pkgs = [
            self.create_package_details(
                url="https://autopatchcn.bh3.com/ptpublic/rel/20241201_8.3.0_full/Audio_Chinese_8.3.0.zip",
                size=2147483648,  # 2GB
                decompressed_size=3221225472,  # 3GB
                language="Chinese"
            ),
            self.create_package_details(
                url="https://autopatchcn.bh3.com/ptpublic/rel/20241201_8.3.0_full/Audio_Japanese_8.3.0.zip",
                size=2684354560,  # 2.5GB
                decompressed_size=4026531840,  # 3.75GB
                language="Japanese"
            )
        ]
        
        # 8.2.0 到 8.3.0 的补丁
        patch_8_2_0 = self.create_version_section(
            version="8.2.0",
            game_packages=[
                self.create_package_details(
                    url="https://autopatchcn.bh3.com/ptpublic/rel/patches/BH3_8.2.0_to_8.3.0_patch.zip",
                    size=1073741824,  # 1GB
                    decompressed_size=2147483648,  # 2GB
                    path="https://autopatchcn.bh3.com/ptpublic/rel/patches/8.2.0_to_8.3.0/",
                    pkg_version_file="pkg_version_patch"
                )
            ],
            audio_packages=[
                self.create_package_details(
                    url="https://autopatchcn.bh3.com/ptpublic/rel/patches/Audio_Chinese_8.2.0_to_8.3.0_patch.zip",
                    size=536870912,  # 512MB
                    decompressed_size=1073741824,  # 1GB
                    language="Chinese"
                ),
                self.create_package_details(
                    url="https://autopatchcn.bh3.com/ptpublic/rel/patches/Audio_Japanese_8.2.0_to_8.3.0_patch.zip",
                    size=671088640,  # 640MB
                    decompressed_size=1342177280,  # 1.25GB
                    language="Japanese"
                )
            ],
            res_list_url="https://autopatchcn.bh3.com/ptpublic/rel/patches/8.2.0_to_8.3.0/pkg_version_patch"
        )
        
        # 8.1.0 到 8.3.0 的补丁
        patch_8_1_0 = self.create_version_section(
            version="8.1.0",
            game_packages=[
                self.create_package_details(
                    url="https://autopatchcn.bh3.com/ptpublic/rel/patches/BH3_8.1.0_to_8.3.0_patch.zip",
                    size=2147483648,  # 2GB
                    decompressed_size=4294967296,  # 4GB
                    path="https://autopatchcn.bh3.com/ptpublic/rel/patches/8.1.0_to_8.3.0/",
                    pkg_version_file="pkg_version_patch"
                )
            ],
            audio_packages=[
                self.create_package_details(
                    url="https://autopatchcn.bh3.com/ptpublic/rel/patches/Audio_Chinese_8.1.0_to_8.3.0_patch.zip",
                    size=1073741824,  # 1GB
                    decompressed_size=2147483648,  # 2GB
                    language="Chinese"
                ),
                self.create_package_details(
                    url="https://autopatchcn.bh3.com/ptpublic/rel/patches/Audio_Japanese_8.1.0_to_8.3.0_patch.zip",
                    size=1342177280,  # 1.25GB
                    decompressed_size=2684354560,  # 2.5GB
                    language="Japanese"
                )
            ],
            res_list_url="https://autopatchcn.bh3.com/ptpublic/rel/patches/8.1.0_to_8.3.0/pkg_version_patch"
        )
        
        # 预下载 8.4.0
        preload_game_pkg = self.create_package_details(
            url="https://autopatchcn.bh3.com/ptpublic/rel/preload/BH3_v8.4.0_preload.zip",
            size=16106127360,  # ~15GB
            decompressed_size=21474836480,  # ~20GB
            path="https://autopatchcn.bh3.com/ptpublic/rel/preload/20241215_8.4.0/",
            pkg_version_file="pkg_version_preload"
        )
        
        preload_audio_pkgs = [
            self.create_package_details(
                url="https://autopatchcn.bh3.com/ptpublic/rel/preload/Audio_Chinese_8.4.0_preload.zip",
                size=2415919104,  # ~2.25GB
                decompressed_size=3623878656,  # ~3.37GB
                language="Chinese"
            ),
            self.create_package_details(
                url="https://autopatchcn.bh3.com/ptpublic/rel/preload/Audio_Japanese_8.4.0_preload.zip",
                size=3019898880,  # ~2.81GB
                decompressed_size=4529848320,  # ~4.22GB
                language="Japanese"
            )
        ]
        
        # 预下载补丁 8.3.0 到 8.4.0
        preload_patch = self.create_version_section(
            version="8.3.0",
            game_packages=[
                self.create_package_details(
                    url="https://autopatchcn.bh3.com/ptpublic/rel/preload/BH3_8.3.0_to_8.4.0_preload_patch.zip",
                    size=1610612736,  # 1.5GB
                    decompressed_size=3221225472,  # 3GB
                    path="https://autopatchcn.bh3.com/ptpublic/rel/preload/8.3.0_to_8.4.0/",
                    pkg_version_file="pkg_version_preload_patch"
                )
            ],
            audio_packages=[
                self.create_package_details(
                    url="https://autopatchcn.bh3.com/ptpublic/rel/preload/Audio_Chinese_8.3.0_to_8.4.0_preload_patch.zip",
                    size=805306368,  # 768MB
                    decompressed_size=1610612736,  # 1.5GB
                    language="Chinese"
                ),
                self.create_package_details(
                    url="https://autopatchcn.bh3.com/ptpublic/rel/preload/Audio_Japanese_8.3.0_to_8.4.0_preload_patch.zip",
                    size=1006632960,  # 960MB
                    decompressed_size=2013265920,  # 1.87GB
                    language="Japanese"
                )
            ],
            res_list_url="https://autopatchcn.bh3.com/ptpublic/rel/preload/8.3.0_to_8.4.0/pkg_version_preload_patch"
        )
        
        # 添加游戏包配置
        self.add_game_package(
            main_version="8.3.0",
            main_game_packages=[main_game_pkg],
            main_audio_packages=main_audio_pkgs,
            patches=[patch_8_2_0, patch_8_1_0],
            preload_version="8.4.0",
            preload_game_packages=[preload_game_pkg],
            preload_audio_packages=preload_audio_pkgs,
            preload_patches=[preload_patch]
        )
        
        return self.config
    
    def save_config(self, filename: str, config: Dict = None):
        """保存配置到文件"""
        if config is None:
            config = self.config
            
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(config, f, indent=2, ensure_ascii=False)
        
        print(f"配置已保存到: {filename}")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="main.patches 配置生成工具",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  # 生成示例配置
  python generate_patches_config.py --example -o example_patches.json
  
  # 生成自定义配置
  python generate_patches_config.py --custom -o custom_patches.json
        """
    )
    
    parser.add_argument('--example', action='store_true',
                       help='生成示例配置')
    parser.add_argument('--custom', action='store_true',
                       help='生成自定义配置（交互式）')
    parser.add_argument('-o', '--output', default='patches_config.json',
                       help='输出文件名 (默认: patches_config.json)')
    
    args = parser.parse_args()
    
    generator = PatchConfigGenerator()
    
    if args.example:
        print("🔧 生成示例配置...")
        config = generator.generate_example_config()
        generator.save_config(args.output, config)
        
        print("\n📋 配置摘要:")
        print(f"   主版本: 8.3.0")
        print(f"   支持补丁: 8.2.0 → 8.3.0, 8.1.0 → 8.3.0")
        print(f"   预下载版本: 8.4.0")
        print(f"   语言支持: 中文, 日文")
        
    elif args.custom:
        print("🛠️  自定义配置生成器")
        print("=" * 40)
        
        # 这里可以添加交互式配置生成逻辑
        print("自定义配置功能开发中...")
        
    else:
        print("请指定 --example 或 --custom 参数")
        parser.print_help()
        return 1
    
    print(f"\n✅ 配置生成完成!")
    print(f"📄 查看配置说明: main_patches_配置说明.md")
    
    return 0


if __name__ == "__main__":
    exit(main())
