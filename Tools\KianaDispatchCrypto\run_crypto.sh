#!/bin/bash
# KianaDispatch Crypto Tool - Unix Shell Script
# ==============================================

set -e

echo "KianaDispatch Crypto Tool"
echo "========================="
echo

# Check if Python is installed
if ! command -v python3 &> /dev/null; then
    echo "Error: Python 3 is not installed or not in PATH"
    echo "Please install Python 3.7 or higher"
    exit 1
fi

# Check if dependencies are installed
if ! python3 -c "import Crypto" &> /dev/null; then
    echo "Installing dependencies..."
    pip3 install -r requirements.txt
    if [ $? -ne 0 ]; then
        echo "Error: Failed to install dependencies"
        exit 1
    fi
    echo "Dependencies installed successfully!"
    echo
fi

# Function to show menu
show_menu() {
    echo "Choose an option:"
    echo "1. Decrypt encrypted response"
    echo "2. Encrypt JSON data"
    echo "3. Auto-detect and process data"
    echo "4. Run test suite"
    echo "5. Show help"
    echo "6. Exit"
    echo
}

# Function for decrypt mode
decrypt_mode() {
    echo
    echo "Decrypt Mode"
    echo "============"
    read -p "Enter dispatcher key (e.g., bgyzmmhy): " key
    read -p "Enter game version (e.g., 8.3): " version
    echo
    echo "Choose input method:"
    echo "1. Enter encrypted data directly"
    echo "2. Read from file"
    read -p "Enter choice (1-2): " input_method
    
    case $input_method in
        1)
            read -p "Enter encrypted data: " data
            python3 kiana_dispatch_crypto.py decrypt -k "$key" -v "$version" -d "$data"
            ;;
        2)
            read -p "Enter file path: " file
            python3 kiana_dispatch_crypto.py decrypt -k "$key" -v "$version" -f "$file"
            ;;
        *)
            echo "Invalid choice."
            ;;
    esac
    echo
    read -p "Press Enter to continue..."
}

# Function for encrypt mode
encrypt_mode() {
    echo
    echo "Encrypt Mode"
    echo "============"
    read -p "Enter dispatcher key (e.g., bgyzmmhy): " key
    read -p "Enter game version (e.g., 8.3): " version
    echo
    echo "Choose input method:"
    echo "1. Enter JSON data directly"
    echo "2. Read from file"
    read -p "Enter choice (1-2): " input_method
    
    case $input_method in
        1)
            read -p "Enter JSON data: " data
            python3 kiana_dispatch_crypto.py encrypt -k "$key" -v "$version" -j "$data"
            ;;
        2)
            read -p "Enter file path: " file
            python3 kiana_dispatch_crypto.py encrypt -k "$key" -v "$version" -f "$file"
            ;;
        *)
            echo "Invalid choice."
            ;;
    esac
    echo
    read -p "Press Enter to continue..."
}

# Function for auto-detect mode
auto_mode() {
    echo
    echo "Auto-detect Mode"
    echo "================"
    read -p "Enter dispatcher key (e.g., bgyzmmhy): " key
    read -p "Enter game version (e.g., 8.3): " version
    echo
    echo "Choose input method:"
    echo "1. Enter data directly"
    echo "2. Read from file"
    read -p "Enter choice (1-2): " input_method
    
    case $input_method in
        1)
            read -p "Enter data: " data
            python3 kiana_dispatch_crypto.py auto -k "$key" -v "$version" -d "$data"
            ;;
        2)
            read -p "Enter file path: " file
            python3 kiana_dispatch_crypto.py auto -k "$key" -v "$version" -f "$file"
            ;;
        *)
            echo "Invalid choice."
            ;;
    esac
    echo
    read -p "Press Enter to continue..."
}

# Function for test mode
test_mode() {
    echo
    echo "Running Test Suite..."
    echo "===================="
    python3 test_crypto.py
    echo
    read -p "Press Enter to continue..."
}

# Function for help mode
help_mode() {
    echo
    echo "Help"
    echo "===="
    python3 kiana_dispatch_crypto.py --help
    echo
    read -p "Press Enter to continue..."
}

# Main menu loop
while true; do
    show_menu
    read -p "Enter your choice (1-6): " choice
    
    case $choice in
        1)
            decrypt_mode
            ;;
        2)
            encrypt_mode
            ;;
        3)
            auto_mode
            ;;
        4)
            test_mode
            ;;
        5)
            help_mode
            ;;
        6)
            echo "Goodbye!"
            exit 0
            ;;
        *)
            echo "Invalid choice. Please try again."
            echo
            ;;
    esac
done
