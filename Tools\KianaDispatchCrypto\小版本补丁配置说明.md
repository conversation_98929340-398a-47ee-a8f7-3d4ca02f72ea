# 小版本补丁配置详解

## 概述

小版本更新（如 8.3.0 → 8.3.1 → 8.3.2 → 8.3.3）是游戏运营中最常见的更新方式，主要用于：
- 🐛 **热修复**：修复关键bug
- ✨ **功能更新**：添加新功能
- ⚡ **性能优化**：提升游戏性能
- 🔒 **安全补丁**：修复安全漏洞

## 🎯 配置策略

### 策略一：**链式补丁**（推荐用于频繁更新）

每个版本只提供从前一个版本的补丁：

```json
{
  "main": {
    "major": { "version": "8.3.3" },
    "patches": [
      { "version": "8.3.2" },  // 8.3.2 → 8.3.3
      { "version": "8.3.1" },  // 8.3.1 → 8.3.2 (需要先更新到8.3.2)
      { "version": "8.3.0" }   // 8.3.0 → 8.3.1 (需要多步更新)
    ]
  }
}
```

**优点：**
- ✅ 补丁包最小
- ✅ 服务器存储成本低
- ✅ 适合频繁发布

**缺点：**
- ❌ 跨版本更新需要多步
- ❌ 更新逻辑复杂

### 策略二：**累积补丁**（推荐用于稳定发布）

每个版本都提供直接到最新版本的补丁：

```json
{
  "main": {
    "major": { "version": "8.3.3" },
    "patches": [
      { "version": "8.3.2" },  // 8.3.2 → 8.3.3 (热修复)
      { "version": "8.3.1" },  // 8.3.1 → 8.3.3 (累积更新)
      { "version": "8.3.0" }   // 8.3.0 → 8.3.3 (完整累积)
    ]
  }
}
```

**优点：**
- ✅ 一步到位更新
- ✅ 用户体验好
- ✅ 更新逻辑简单

**缺点：**
- ❌ 补丁包较大
- ❌ 存储成本高

## 📊 补丁大小规划

### 典型大小分布

| 更新类型 | 版本跨度 | 典型大小 | 用途 |
|----------|----------|----------|------|
| **热修复** | 8.3.2→8.3.3 | 50-200MB | 紧急bug修复 |
| **小更新** | 8.3.1→8.3.3 | 200-500MB | 功能更新 |
| **累积更新** | 8.3.0→8.3.3 | 300-800MB | 完整小版本更新 |
| **主版本更新** | 8.2.0→8.3.3 | 1-3GB | 跨主版本更新 |

### 大小优化策略

```json
{
  "patches": [
    {
      "version": "8.3.2",
      "game_pkgs": [{
        "url": "hotfix_8.3.2_to_8.3.3.zip",
        "size": "134217728",        // ~128MB (热修复)
        "decompressed_size": "268435456"
      }]
    },
    {
      "version": "8.3.1", 
      "game_pkgs": [{
        "url": "update_8.3.1_to_8.3.3.zip",
        "size": "268435456",        // ~256MB (功能更新)
        "decompressed_size": "536870912"
      }]
    },
    {
      "version": "8.3.0",
      "game_pkgs": [{
        "url": "cumulative_8.3.0_to_8.3.3.zip", 
        "size": "402653184",        // ~384MB (累积更新)
        "decompressed_size": "805306368"
      }]
    }
  ]
}
```

## 🔄 更新流程设计

### 智能更新逻辑

```mermaid
graph TD
    A[检测当前版本] --> B{版本类型}
    B -->|8.3.2| C[热修复补丁 ~128MB]
    B -->|8.3.1| D[功能更新补丁 ~256MB]
    B -->|8.3.0| E[累积补丁 ~384MB]
    B -->|8.2.x| F[主版本补丁 ~1GB]
    B -->|更老版本| G[完整包下载]
    
    C --> H[应用补丁]
    D --> H
    E --> H
    F --> H
    G --> I[完整安装]
    H --> J[更新完成]
    I --> J
```

### 版本匹配优先级

1. **精确匹配**：当前版本 = patches[i].version
2. **最近匹配**：选择最接近的较新版本
3. **回退策略**：使用 major 完整包

## 🛠️ 实际配置示例

### 崩坏3小版本更新配置

```json
{
  "main": {
    "major": {
      "version": "8.3.3",
      "game_pkgs": [
        {
          "url": "https://autopatchcn.bh3.com/BH3_v8.3.3_full.zip",
          "size": "15932170240",  // ~14.8GB
          "decompressed_size": "21206425600"  // ~19.7GB
        }
      ]
    },
    "patches": [
      {
        "version": "8.3.2",
        "game_pkgs": [
          {
            "url": "https://autopatchcn.bh3.com/patches/BH3_8.3.2_to_8.3.3_hotfix.zip",
            "size": "134217728",  // 128MB 热修复
            "decompressed_size": "268435456",
            "pkg_version_file_name": "pkg_version_hotfix"
          }
        ]
      },
      {
        "version": "8.3.1", 
        "game_pkgs": [
          {
            "url": "https://autopatchcn.bh3.com/patches/BH3_8.3.1_to_8.3.3_update.zip",
            "size": "268435456",  // 256MB 功能更新
            "decompressed_size": "536870912",
            "pkg_version_file_name": "pkg_version_update"
          }
        ]
      },
      {
        "version": "8.3.0",
        "game_pkgs": [
          {
            "url": "https://autopatchcn.bh3.com/patches/BH3_8.3.0_to_8.3.3_cumulative.zip", 
            "size": "402653184",  // 384MB 累积更新
            "decompressed_size": "805306368",
            "pkg_version_file_name": "pkg_version_cumulative"
          }
        ]
      }
    ]
  }
}
```

## 🎯 最佳实践

### 1. 版本命名规范

```
主版本.次版本.修订版本
8.3.0 - 基础版本
8.3.1 - 功能更新
8.3.2 - 性能优化  
8.3.3 - 热修复
```

### 2. 补丁文件命名

```
BH3_{源版本}_to_{目标版本}_{类型}.zip

示例：
BH3_8.3.2_to_8.3.3_hotfix.zip     // 热修复
BH3_8.3.1_to_8.3.3_update.zip     // 功能更新
BH3_8.3.0_to_8.3.3_cumulative.zip // 累积更新
```

### 3. pkg_version 文件管理

```json
{
  "version": "8.3.2",
  "game_pkgs": [{
    "pkg_version_file_name": "pkg_version_hotfix"  // 区分不同类型的补丁
  }]
}
```

### 4. 语音包策略

```json
{
  "audio_pkgs": [
    {
      "language": "Chinese",
      "url": "Audio_Chinese_8.3.2_to_8.3.3_hotfix.zip",
      "size": "67108864"  // 语音包通常较小
    }
  ]
}
```

## 🚀 部署建议

### 1. 发布策略

- **热修复**：立即发布，强制更新
- **功能更新**：定期发布，推荐更新
- **累积更新**：月度发布，包含所有改进

### 2. 回滚准备

```json
{
  "rollback": {
    "from_version": "8.3.3",
    "to_version": "8.3.2", 
    "rollback_pkg": "BH3_8.3.3_to_8.3.2_rollback.zip"
  }
}
```

### 3. 测试验证

- ✅ 每个补丁的完整性测试
- ✅ 跨版本兼容性测试
- ✅ 回滚功能测试
- ✅ 性能影响评估

## 💡 注意事项

### 1. 版本兼容性

确保补丁不会破坏：
- 存档兼容性
- 配置文件格式
- 插件接口

### 2. 用户体验

- 显示准确的更新大小
- 提供更新内容说明
- 支持后台下载
- 提供更新进度

### 3. 错误处理

- 补丁下载失败 → 重试机制
- 补丁应用失败 → 回退到完整包
- 版本检测错误 → 强制完整更新

这种小版本补丁配置方式能够最大化用户体验，同时保持服务器资源的合理使用！
