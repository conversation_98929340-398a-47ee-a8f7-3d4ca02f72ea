@echo off
chcp 65001 >nul
REM 解密您提供的示例数据
REM ====================

echo 解密 KianaDispatch 示例数据
echo ===========================
echo.

REM 检查 Python 是否已安装
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误: 未安装 Python 或 Python 不在 PATH 中
    echo 请先运行 "快速测试.bat" 进行环境设置
    pause
    exit /b 1
)

REM 检查依赖是否已安装
python -c "import Crypto" >nul 2>&1
if errorlevel 1 (
    echo 错误: 未安装依赖包
    echo 请先运行 "快速测试.bat" 进行环境设置
    pause
    exit /b 1
)

echo 配置信息:
echo - 调度密钥 (DispatcherKey): bgyzmmhy
echo - 游戏版本: 8.3
echo - 数据来源: 崩坏3国服 dispatch 响应
echo.

echo 正在解密数据...
echo ================
echo.

REM 解密您提供的实际数据
python kiana_dispatch_crypto.py decrypt -k bgyzmmhy -v 8.3 -d "oG7PvQY0q1ZqrCiH6s+sz1gHfjY8hckVk5vCRUbXBt3v8/SOWjLo1y4eWPGQDflNFSStynuuBnqex38/pMz94JLVG5uyRlqh25etOw1P+kbKGGFgGd/6hkdq/QJydibSdJq3FvgKyL410DrPMw43Zg3el4XixA8OBEdNHHKyw1VSBfTqLbOwOjj1rFQS+SS3pxZS41sWqLo3uV2W4dX732rEV94xQhyLw6shpgWFCIUofAmss3IRleiM7/yPlwuOjuA77WUnl1GDEW7ZaehglBP3ohmx/KSkmUJfiR/Z1QnCeXFpHQOGp7yI7drrBYSoDxs1/aPQFqmPbdj2vC0wrUx+QTUA1VoQFceCcIvB/gyDa4ym7fiWLTqqJNlelTg0arL1v/Y8SQKZI+fStAiNMtv8MFysHo51r3bnDvsZZRgedoEykDS2TG3v5LyHWajx4fkRupt+fFPijSHjKa7W7qnLRM2s/Cyxty8ROfFqG7MH2/TZ7J5uKuprOgB16uwkTr1Hdx9loRgxt3+FEsy5rnkADtqnnCDZZCynEICqCV8oawHILD7kDStLKWLZWsoqSxbZsJWPtm1RTf4/ZSYRZSTdenuki9U4Q5ClGjX7UuCt8I8Q7+lbbfxIqH8YqDOivyw9zF6HQsmJ8lKoLq4GwPnX5EorAetD9jMfW/hagD3A7cYeQ1t6+OueTaP9H+XxfpkeFqIGUBRl7zsOWPjfdBKLaZ98Kmi0z8JawNjozSDEkV8XgfhWpMJ9o9N18AUzQAp3jVgqwq5BG6M/sx0HVzcu42uvQddlPYVQpv7Zwrto3UxqzKUnGZCzN2j4/QmysOl7T1GquDJhZmvFNWJT2Y0oX17jNW8aWGWJALxjXKGdJmB2F6K7ukIVopJwXUhJyRMLlLFRxbtEOkFAk8ZhyH34hdqpE1XQ6EELQy43Iq2r1eZ5JIuCpEtC+3rRz2nxQ0oyfTPsBRFt5FRgSlcHAM6WRbstfnqpBbLDYCEIHTMaE1/uwfowSWcBAZDgeZrAWuDmvLILCD7lqmV4No7JEQM7O82kgQ4+oUSoniq7eIGdJmB2F6K7ukIVopJwXUhJyRMLlLFRxbtEOkFAk8ZhyB3sT0is1VTpJT33y1hycLFN5GD+S3uNCg1VEIaKNgiSpWWiZN3mPD0HsPAyr+sZGUScJCxy9jGVfmfu8abxBeyTAvWkZF+q2EZpLrqZZyzzHooLJvyfQDzQDGLodFz9EQfb9Nnsnm4q6ms6AHXq7CROvUd3H2WhGDG3f4USzLmuxGrZuEqUYQnOb3MC0dKyCxE8HrUcUeywiTjmpjQf1Ar2htym6MixvuUiqz3SGnWzQy+OkG4VbRluFC7oocJK7TlK1HJ3x/xiXw3+hpdqK1Y="

if errorlevel 1 (
    echo.
    echo 解密失败！请检查:
    echo 1. 调度密钥是否正确
    echo 2. 游戏版本是否正确
    echo 3. 加密数据是否完整
) else (
    echo.
    echo ================
    echo 解密成功！
    echo.
    echo 上面显示的就是解密后的 JSON 数据，包含了:
    echo retcode: 返回码
    echo message: 返回消息
    echo region_list: 服务器区域列表
    echo   name: 服务器名称
    echo   title: 服务器标题
    echo   dispatch_url: 分发服务器 URL
    echo   asset_bundle_url_list: 资源包 URL 列表
    echo   ex_resource_url_list: 扩展资源 URL 列表
)

echo.
echo 您也可以将结果保存到文件:
echo python kiana_dispatch_crypto.py decrypt -k bgyzmmhy -v 8.3 -f examples\sample_encrypted_response.txt -o 解密结果.json
echo.

pause
