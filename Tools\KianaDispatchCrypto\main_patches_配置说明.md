# main.patches 配置详解

## 概述

`main.patches` 是 HoYoPlay 启动器 API 中用于配置增量更新补丁的重要部分。它允许游戏从特定版本直接更新到最新版本，而无需下载完整的安装包。

## 📋 配置结构

### 顶层结构

```json
{
  "retcode": 0,
  "message": "OK", 
  "data": {
    "game_packages": [
      {
        "game": { ... },
        "main": {
          "major": { ... },      // 完整版本
          "patches": [ ... ]     // 增量补丁数组
        },
        "pre_download": {
          "major": { ... },      // 预下载完整版本
          "patches": [ ... ]     // 预下载增量补丁数组
        }
      }
    ]
  }
}
```

### 核心字段说明

#### 1. `main.major` - 完整版本
```json
{
  "version": "8.3.0",           // 目标版本号
  "game_pkgs": [ ... ],         // 游戏主包
  "audio_pkgs": [ ... ],        // 语音包
  "res_list_url": "..."         // 资源列表URL (pkg_version)
}
```

#### 2. `main.patches` - 增量补丁数组
```json
[
  {
    "version": "8.2.0",         // 源版本号 (从此版本更新)
    "game_pkgs": [ ... ],       // 补丁包
    "audio_pkgs": [ ... ],      // 语音补丁包
    "res_list_url": "..."       // 补丁资源列表URL
  },
  {
    "version": "8.1.0",         // 另一个源版本
    "game_pkgs": [ ... ],
    "audio_pkgs": [ ... ],
    "res_list_url": "..."
  }
]
```

## 🔧 详细配置字段

### PackageDetails 结构

每个包（game_pkgs 和 audio_pkgs 中的项目）都包含以下字段：

```json
{
  "url": "https://example.com/package.zip",           // 下载URL
  "md5": "a1b2c3d4e5f6...",                          // MD5校验值
  "size": "1073741824",                              // 压缩包大小（字节）
  "decompressed_size": "2147483648",                 // 解压后大小（字节）
  "path": "https://example.com/extracted/",          // 解压基础路径
  "pkg_version_file_name": "pkg_version",            // pkg_version文件名
  "language": "Chinese"                              // 语言（仅audio_pkgs）
}
```

### 字段详解

| 字段 | 类型 | 必需 | 说明 |
|------|------|------|------|
| `url` | string | ✅ | 包的下载URL |
| `md5` | string | ✅ | MD5校验值，用于验证下载完整性 |
| `size` | string/number | ✅ | 压缩包大小（字节） |
| `decompressed_size` | string/number | ✅ | 解压后大小（字节） |
| `path` | string | ❌ | 解压后的基础路径URL |
| `pkg_version_file_name` | string | ❌ | pkg_version文件名 |
| `language` | string | ❌ | 语言标识（仅用于audio_pkgs） |

## 🎯 工作原理

### 1. 版本匹配逻辑

Collapse Launcher 会：
1. 检测当前安装的游戏版本
2. 在 `patches` 数组中查找匹配的源版本
3. 如果找到匹配项，使用对应的补丁包
4. 如果没有找到，使用 `major` 中的完整包

### 2. 更新流程

```mermaid
graph TD
    A[检测当前版本] --> B{在patches中找到匹配?}
    B -->|是| C[下载增量补丁]
    B -->|否| D[下载完整包]
    C --> E[应用补丁]
    D --> F[完整安装]
    E --> G[更新完成]
    F --> G
```

### 3. 补丁优先级

- **最高优先级**: 精确版本匹配的补丁
- **中等优先级**: 较新版本的补丁（如果支持跨版本）
- **最低优先级**: 完整包下载

## 📝 配置示例

### 基础配置

```json
{
  "main": {
    "major": {
      "version": "8.3.0",
      "game_pkgs": [
        {
          "url": "https://cdn.example.com/game_v8.3.0_full.zip",
          "md5": "abc123...",
          "size": "15728640000",
          "decompressed_size": "20971520000"
        }
      ]
    },
    "patches": [
      {
        "version": "8.2.0",
        "game_pkgs": [
          {
            "url": "https://cdn.example.com/patch_8.2.0_to_8.3.0.zip",
            "md5": "def456...",
            "size": "1073741824",
            "decompressed_size": "2147483648"
          }
        ]
      }
    ]
  }
}
```

### 多语言支持

```json
{
  "patches": [
    {
      "version": "8.2.0",
      "game_pkgs": [ ... ],
      "audio_pkgs": [
        {
          "language": "Chinese",
          "url": "https://cdn.example.com/audio_cn_patch.zip",
          "md5": "ghi789...",
          "size": "536870912",
          "decompressed_size": "1073741824"
        },
        {
          "language": "Japanese", 
          "url": "https://cdn.example.com/audio_jp_patch.zip",
          "md5": "jkl012...",
          "size": "671088640",
          "decompressed_size": "1342177280"
        }
      ]
    }
  ]
}
```

## 🚀 最佳实践

### 1. 版本策略

- **支持最近3-5个版本**的增量更新
- **较老版本**直接使用完整包
- **预下载**提前准备下一版本

### 2. 包大小优化

- **增量补丁**应显著小于完整包
- **语音包**单独提供补丁
- **分段下载**支持大文件

### 3. 容错设计

- **MD5校验**确保下载完整性
- **回退机制**：补丁失败时使用完整包
- **多CDN支持**提高可用性

## 🔧 实际应用

### 崩坏3东南亚服示例

```json
{
  "main": {
    "patches": [
      {
        "version": "8.2.0",
        "game_pkgs": [
          {
            "url": "https://autopatchsea.bh3.com/patches/BH3_8.2.0_to_8.3.0_SEA.zip",
            "md5": "sea123...",
            "size": "1073741824",
            "decompressed_size": "2147483648"
          }
        ],
        "audio_pkgs": [
          {
            "language": "English",
            "url": "https://autopatchsea.bh3.com/patches/Audio_EN_8.2.0_to_8.3.0.zip",
            "md5": "sea456...",
            "size": "536870912",
            "decompressed_size": "1073741824"
          }
        ]
      }
    ]
  }
}
```

## 💡 注意事项

### 1. 版本兼容性
- 确保补丁版本号与实际游戏版本匹配
- 测试跨版本更新的兼容性

### 2. 网络优化
- 使用CDN加速下载
- 支持断点续传
- 提供多个下载源

### 3. 用户体验
- 显示准确的下载进度
- 提供更新大小预估
- 支持后台下载

这种配置方式大大减少了用户的下载量，提高了更新效率，是现代游戏启动器的标准做法！
