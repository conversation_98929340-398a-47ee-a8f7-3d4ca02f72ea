@echo off
REM KianaDispatch Crypto Tool - Windows Batch Script
REM ================================================

setlocal enabledelayedexpansion

echo KianaDispatch Crypto Tool
echo =========================
echo.

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo Error: Python is not installed or not in PATH
    echo Please install Python 3.7 or higher from https://python.org
    pause
    exit /b 1
)

REM Check if dependencies are installed
python -c "import Crypto" >nul 2>&1
if errorlevel 1 (
    echo Installing dependencies...
    pip install -r requirements.txt
    if errorlevel 1 (
        echo Error: Failed to install dependencies
        pause
        exit /b 1
    )
    echo Dependencies installed successfully!
    echo.
)

REM Show usage menu
:menu
echo Choose an option:
echo 1. Decrypt encrypted response
echo 2. Encrypt JSON data
echo 3. Auto-detect and process data
echo 4. Run test suite
echo 5. Show help
echo 6. Exit
echo.
set /p choice="Enter your choice (1-6): "

if "%choice%"=="1" goto decrypt
if "%choice%"=="2" goto encrypt
if "%choice%"=="3" goto auto
if "%choice%"=="4" goto test
if "%choice%"=="5" goto help
if "%choice%"=="6" goto exit
echo Invalid choice. Please try again.
echo.
goto menu

:decrypt
echo.
echo Decrypt Mode
echo ============
set /p key="Enter dispatcher key (e.g., bgyzmmhy): "
set /p version="Enter game version (e.g., 8.3): "
echo.
echo Choose input method:
echo 1. Enter encrypted data directly
echo 2. Read from file
set /p input_method="Enter choice (1-2): "

if "%input_method%"=="1" (
    set /p data="Enter encrypted data: "
    python kiana_dispatch_crypto.py decrypt -k "!key!" -v "!version!" -d "!data!"
) else if "%input_method%"=="2" (
    set /p file="Enter file path: "
    python kiana_dispatch_crypto.py decrypt -k "!key!" -v "!version!" -f "!file!"
) else (
    echo Invalid choice.
)
echo.
pause
goto menu

:encrypt
echo.
echo Encrypt Mode
echo ============
set /p key="Enter dispatcher key (e.g., bgyzmmhy): "
set /p version="Enter game version (e.g., 8.3): "
echo.
echo Choose input method:
echo 1. Enter JSON data directly
echo 2. Read from file
set /p input_method="Enter choice (1-2): "

if "%input_method%"=="1" (
    set /p data="Enter JSON data: "
    python kiana_dispatch_crypto.py encrypt -k "!key!" -v "!version!" -j "!data!"
) else if "%input_method%"=="2" (
    set /p file="Enter file path: "
    python kiana_dispatch_crypto.py encrypt -k "!key!" -v "!version!" -f "!file!"
) else (
    echo Invalid choice.
)
echo.
pause
goto menu

:auto
echo.
echo Auto-detect Mode
echo ================
set /p key="Enter dispatcher key (e.g., bgyzmmhy): "
set /p version="Enter game version (e.g., 8.3): "
echo.
echo Choose input method:
echo 1. Enter data directly
echo 2. Read from file
set /p input_method="Enter choice (1-2): "

if "%input_method%"=="1" (
    set /p data="Enter data: "
    python kiana_dispatch_crypto.py auto -k "!key!" -v "!version!" -d "!data!"
) else if "%input_method%"=="2" (
    set /p file="Enter file path: "
    python kiana_dispatch_crypto.py auto -k "!key!" -v "!version!" -f "!file!"
) else (
    echo Invalid choice.
)
echo.
pause
goto menu

:test
echo.
echo Running Test Suite...
echo =====================
python test_crypto.py
echo.
pause
goto menu

:help
echo.
echo Help
echo ====
python kiana_dispatch_crypto.py --help
echo.
pause
goto menu

:exit
echo Goodbye!
exit /b 0
